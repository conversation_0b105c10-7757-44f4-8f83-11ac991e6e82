#!/usr/bin/env node

/**
 * 构建分析脚本
 * 用于分析打包后的文件大小和依赖关系
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 获取文件大小
function getFileSize(filePath) {
  const stats = fs.statSync(filePath)
  return stats.size
}

// 格式化文件大小
function formatSize(bytes) {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 分析构建结果
function analyzeBuild() {
  const distPath = path.resolve(__dirname, '../dist')
  
  if (!fs.existsSync(distPath)) {
    console.error('❌ 构建目录不存在，请先运行 npm run build')
    process.exit(1)
  }

  console.log('📊 构建分析报告')
  console.log('=' * 50)

  // 分析 JS 文件
  const jsPath = path.join(distPath, 'assets/js')
  if (fs.existsSync(jsPath)) {
    const jsFiles = fs.readdirSync(jsPath)
    let totalJsSize = 0

    console.log('\n📦 JavaScript 文件:')
    jsFiles.forEach(file => {
      const filePath = path.join(jsPath, file)
      const size = getFileSize(filePath)
      totalJsSize += size
      console.log(`  ${file}: ${formatSize(size)}`)
    })
    console.log(`  总计: ${formatSize(totalJsSize)}`)
  }

  // 分析 CSS 文件
  const cssPath = path.join(distPath, 'assets/css')
  if (fs.existsSync(cssPath)) {
    const cssFiles = fs.readdirSync(cssPath)
    let totalCssSize = 0

    console.log('\n🎨 CSS 文件:')
    cssFiles.forEach(file => {
      const filePath = path.join(cssPath, file)
      const size = getFileSize(filePath)
      totalCssSize += size
      console.log(`  ${file}: ${formatSize(size)}`)
    })
    console.log(`  总计: ${formatSize(totalCssSize)}`)
  }

  // 分析压缩文件
  console.log('\n🗜️ 压缩文件:')
  const findCompressedFiles = (dir) => {
    const files = fs.readdirSync(dir, { withFileTypes: true })
    files.forEach(file => {
      const fullPath = path.join(dir, file.name)
      if (file.isDirectory()) {
        findCompressedFiles(fullPath)
      } else if (file.name.endsWith('.gz') || file.name.endsWith('.br')) {
        const originalFile = fullPath.replace(/\.(gz|br)$/, '')
        if (fs.existsSync(originalFile)) {
          const originalSize = getFileSize(originalFile)
          const compressedSize = getFileSize(fullPath)
          const ratio = ((1 - compressedSize / originalSize) * 100).toFixed(1)
          console.log(`  ${file.name}: ${formatSize(compressedSize)} (压缩率: ${ratio}%)`)
        }
      }
    })
  }
  findCompressedFiles(distPath)

  console.log('\n✅ 分析完成!')
}

analyzeBuild()
