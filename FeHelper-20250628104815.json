{"info": {"title": "go", "description": "", "version": "1.0.0", "x-marks": [{"mark_id": "1", "project_id": "0", "name": "开发中", "color": "#2857FF", "is_sys_default": 1, "is_default_mark": 1}, {"mark_id": "2", "project_id": "0", "name": "已完成", "color": "#26CEA4", "is_sys_default": 1, "is_default_mark": -1}, {"mark_id": "3", "project_id": "0", "name": "需修改", "color": "#FFC01E", "is_sys_default": 1, "is_default_mark": -1}, {"mark_id": "4", "project_id": "0", "name": "已废弃", "color": "#FF2200", "is_sys_default": 1, "is_default_mark": -1}]}, "openapi": "3.0.0", "tags": [{"name": "前端", "description": "", "x-type": "folder", "x-url": "", "x-target-id": "b03379381002"}, {"name": "订单管理", "description": "", "x-type": "folder", "x-url": "", "x-target-id": "1d432cd4381034"}, {"name": "疗愈", "description": "", "x-type": "folder", "x-url": "", "x-target-id": "1de8caa2f81001"}, {"name": "后台", "description": "", "x-type": "folder", "x-url": "", "x-target-id": "1dea4410b81009"}, {"name": "配置", "description": "", "x-type": "folder", "x-url": "", "x-target-id": "2379559f31d021"}, {"name": "用户管理", "description": "", "x-type": "folder", "x-url": "", "x-target-id": "2604ab9e381005"}, {"name": "商品", "description": "", "x-type": "folder", "x-url": "", "x-target-id": "2a21f2d3fb800a"}], "paths": {"{{host}}/front/index/index": {"get": {"x-target-id": "b03e63f81003", "x-protocol": "http", "summary": "首页", "description": "", "tags": ["疗愈/前端"], "x-mark-id": "1", "x-updated-at": "2025-04-14T10:39:49+08:00", "x-updated-user-name": "大王子", "parameters": [{"name": "page", "in": "query", "description": "", "required": true, "example": "1", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "description": "", "required": true, "example": "10", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "{{host}}/front/user/login": {"post": {"x-target-id": "b1a5d4f81013", "x-protocol": "http", "summary": "登录", "description": "", "tags": ["疗愈/前端"], "x-mark-id": "1", "x-updated-at": "2025-04-14T10:37:00+08:00", "x-updated-user-name": "大王子", "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "{{host}}/front/index/detail": {"get": {"x-target-id": "b237d0b81017", "x-protocol": "http", "summary": "详情", "description": "", "tags": ["疗愈/前端"], "x-mark-id": "1", "x-updated-at": "2025-04-14T15:36:29+08:00", "x-updated-user-name": "大王子", "parameters": [{"name": "id", "in": "query", "description": "", "required": true, "example": "20", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "{{host}}/front/order/order": {"post": {"x-target-id": "34ab8e7b81011", "x-protocol": "http", "summary": "预约", "description": "", "tags": ["疗愈/前端"], "x-mark-id": "1", "x-updated-at": "2025-06-03T15:43:38+08:00", "x-updated-user-name": "大王子", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"id": {"type": "string", "description": "商品id", "example": "20"}, "product_time_id": {"type": "string", "description": "时间id", "example": "16"}, "remark": {"type": "string", "description": "备注", "example": "谁谁谁"}}}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "{{host}}/front/order/details": {"get": {"x-target-id": "1d4146b6b81032", "x-protocol": "http", "summary": "预约详情", "description": "", "tags": ["疗愈/前端"], "x-mark-id": "1", "x-updated-at": "2025-05-06T16:37:23+08:00", "x-updated-user-name": "大王子", "parameters": [{"name": "order_no", "in": "query", "description": "", "required": true, "example": "20250506150051410", "schema": {"type": "string"}}], "requestBody": {"content": {}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "{{host}}/admin/order/list": {"get": {"x-target-id": "1d433adc381035", "x-protocol": "http", "summary": "列表", "description": "", "tags": ["疗愈/后台/订单管理"], "x-mark-id": "1", "x-updated-at": "2025-05-06T15:25:00+08:00", "x-updated-user-name": "大王子", "parameters": [{"name": "page", "in": "query", "description": "", "required": true, "example": "1", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "description": "", "required": true, "example": "10", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": "{\n\t\"code\": 200,\n\t\"msg\": \"成功\",\n\t\"data\": {\n\t\t\"list\": [\n\t\t\t{\n\t\t\t\t\"attach\": null,\n\t\t\t\t\"create_time\": \"2025-05-06 15:00:52\",\n\t\t\t\t\"id\": 1,\n\t\t\t\t\"nickname\": \"昵称\",\n\t\t\t\t\"product_id\": 20,\n\t\t\t\t\"product_name\": \"高级瑜伽课程\",\n\t\t\t\t\"product_time_id\": 16,\n\t\t\t\t\"remark\": \"谁谁谁\",\n\t\t\t\t\"status\": 0,\n\t\t\t\t\"trade_no\": 20250506150051410,\n\t\t\t\t\"unionid\": \"123456\",\n\t\t\t\t\"update_time\": \"2025-05-06 15:00:52\",\n\t\t\t\t\"use_time\": \"2025-04-18T18:26:39+08:00\",\n\t\t\t\t\"user_id\": 1\n\t\t\t}\n\t\t],\n\t\t\"total\": 1\n\t}\n}"}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "{{host}}/front/order/list": {"get": {"x-target-id": "1d56d65738107d", "x-protocol": "http", "summary": "预约列表", "description": "", "tags": ["疗愈/前端"], "x-mark-id": "1", "x-updated-at": "2025-05-06T16:38:00+08:00", "x-updated-user-name": "大王子", "parameters": [{"name": "page", "in": "query", "description": "", "required": true, "example": "1", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "description": "", "required": true, "example": "10", "schema": {"type": "string"}}], "requestBody": {"content": {}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": "{\r\n    \"code\": 200,\r\n    \"msg\": \"成功\",\r\n    \"data\": [\r\n        {\r\n            \"attach\": null,\r\n            \"create_time\": \"2025-05-06 15:00:52\",\r\n            \"id\": 1,\r\n            \"nickname\": \"昵称\",\r\n            \"product_id\": 20,\r\n            \"product_name\": \"高级瑜伽课程\",\r\n            \"product_time_id\": 16,\r\n            \"remark\": \"谁谁谁\",\r\n            \"status\": 0,\r\n            \"trade_no\": 20250506150051410,\r\n            \"unionid\": \"123456\",\r\n            \"update_time\": \"2025-05-06 15:00:52\",\r\n            \"use_time\": \"2025-04-18 18:26:39\",\r\n            \"user_id\": 1\r\n        }\r\n    ]\r\n}"}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "{{host}}/admin/login": {"post": {"x-target-id": "1de8dee3f81002", "x-protocol": "http", "summary": "登录", "description": "", "tags": ["疗愈/后台"], "x-mark-id": "1", "x-updated-at": "2025-03-22T16:29:15+08:00", "x-updated-user-name": "大王子", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"username": {"type": "string", "description": "", "example": "admin"}, "password": {"type": "string", "description": "", "example": "1234"}}}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": "{\r\n    \"code\": 200,\r\n    \"msg\": \"登录成功\",\r\n    \"data\": \"349017e05f1b8b05522014300b7e93f709ad0e56dbb8e9ec758efe176fedad5d36e3574401b7b795e585ca4ac2be09ddce9c5c995458d41f52d900aff8d3fb06\"\r\n}"}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "http://127.0.0.1:5000/ocr": {"post": {"x-target-id": "1dea70b578100a", "x-protocol": "http", "summary": "新建接口", "description": "", "tags": ["疗愈/后台"], "x-mark-id": "1", "x-updated-at": "2025-03-26T10:10:39+08:00", "x-updated-user-name": "大王子", "parameters": [{"name": "Content-Type", "in": "header", "description": "", "required": true, "example": "application/json", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}, "example": "{\r\n\"image\":\"data:image/png;base64,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\"\r\n}"}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "{{host}}/admin/test": {"get": {"x-target-id": "1e012b8cb81022", "x-protocol": "http", "summary": "测试", "description": "", "tags": ["疗愈/后台"], "x-mark-id": "1", "x-updated-at": "2025-03-22T16:35:09+08:00", "x-updated-user-name": "大王子", "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": "{\r\n    \"code\": 200,\r\n    \"msg\": \"登录成功\",\r\n    \"data\": \"349017e05f1b8b05522014300b7e93f709ad0e56dbb8e9ec758efe176fedad5d36e3574401b7b795e585ca4ac2be09ddce9c5c995458d41f52d900aff8d3fb06\"\r\n}"}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "{{host}}/admin/upload": {"post": {"x-target-id": "20910206f81007", "x-protocol": "http", "summary": "上传", "description": "", "tags": ["疗愈/后台"], "x-mark-id": "1", "x-updated-at": "2025-04-01T13:47:58+08:00", "x-updated-user-name": "大王子", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "description": "", "example": "C:\\Users\\<USER>\\Desktop\\demo2.jpg", "format": "binary"}}}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": "{\r\n    \"code\": 200,\r\n    \"msg\": \"登录成功\",\r\n    \"data\": \"349017e05f1b8b05522014300b7e93f709ad0e56dbb8e9ec758efe176fedad5d36e3574401b7b795e585ca4ac2be09ddce9c5c995458d41f52d900aff8d3fb06\"\r\n}"}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "{{host}}/admin/config/edit": {"post": {"x-target-id": "2379604171d022", "x-protocol": "http", "summary": "编辑", "description": "", "tags": ["疗愈/后台/配置"], "x-mark-id": "1", "x-updated-at": "2025-03-23T21:10:01+08:00", "x-updated-user-name": "大王子", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "", "example": "123"}, "value": {"type": "string", "description": "", "example": "345ss"}, "desc": {"type": "string", "description": "", "example": "123sdf"}, "id": {"type": "string", "description": "", "example": "2"}}}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": "{\n    \"code\": 200,\n    \"msg\": \"成功\",\n    \"data\": null\n}"}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "{{host}}/admin/config/list": {"get": {"x-target-id": "23a02d00b1d04d", "x-protocol": "http", "summary": "列表", "description": "", "tags": ["疗愈/后台/配置"], "x-mark-id": "1", "x-updated-at": "2025-03-22T20:50:33+08:00", "x-updated-user-name": "大王子", "parameters": [{"name": "page", "in": "query", "description": "", "required": true, "example": "1", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "description": "", "required": true, "example": "10", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": "{\n\t\"code\": 41,\n\t\"msg\": \"成功\",\n\t\"data\": {\n\t\t\"list\": [\n\t\t\t{\n\t\t\t\t\"config_name\": \"123\",\n\t\t\t\t\"config_value\": \"345\",\n\t\t\t\t\"create_time\": null,\n\t\t\t\t\"desc\": \"6666\",\n\t\t\t\t\"id\": 86,\n\t\t\t\t\"update_time\": null\n\t\t\t}\n\t\t],\n\t\t\"total\": 87\n\t}\n}"}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "{{host}}/admin/config/del": {"get": {"x-target-id": "23a6ea5a7b8073", "x-protocol": "http", "summary": "删除", "description": "", "tags": ["疗愈/后台/配置", "疗愈/后台/用户管理"], "x-mark-id": "1", "x-updated-at": "2025-03-22T21:16:48+08:00", "x-updated-user-name": "大王子", "parameters": [{"name": "id", "in": "query", "description": "", "required": true, "example": "1", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": "{\n\t\"code\": 41,\n\t\"msg\": \"成功\",\n\t\"data\": {\n\t\t\"list\": [\n\t\t\t{\n\t\t\t\t\"config_name\": \"123\",\n\t\t\t\t\"config_value\": \"345\",\n\t\t\t\t\"create_time\": null,\n\t\t\t\t\"desc\": \"6666\",\n\t\t\t\t\"id\": 86,\n\t\t\t\t\"update_time\": null\n\t\t\t}\n\t\t],\n\t\t\"total\": 87\n\t}\n}"}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "{{host}}/admin/admin/edit": {"post": {"x-target-id": "2604ab9e381006", "x-protocol": "http", "summary": "编辑", "description": "", "tags": ["疗愈/后台/用户管理"], "x-mark-id": "1", "x-updated-at": "2025-03-25T11:24:17+08:00", "x-updated-user-name": "大王子", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"username": {"type": "string", "description": "", "example": "123"}, "password": {"type": "string", "description": "", "example": "345ss"}, "id": {"type": "string", "description": "", "example": "10"}}}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": "{\n    \"code\": 200,\n    \"msg\": \"成功\",\n    \"data\": {\n        \"id\": 10,\n        \"password\": \"\",\n        \"username\": \"\"\n    }\n}"}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "{{host}}/admin/admin/list": {"get": {"x-target-id": "2604ab9e381007", "x-protocol": "http", "summary": "列表", "description": "", "tags": ["疗愈/后台/用户管理"], "x-mark-id": "1", "x-updated-at": "2025-03-24T17:36:39+08:00", "x-updated-user-name": "大王子", "parameters": [{"name": "page", "in": "query", "description": "", "required": true, "example": "1", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "description": "", "required": true, "example": "10", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": "{\n\t\"code\": 200,\n\t\"msg\": \"成功\",\n\t\"data\": {\n\t\t\"list\": [\n\t\t\t{\n\t\t\t\t\"create_time\": \"2024-06-18 17:27:00\",\n\t\t\t\t\"id\": 1,\n\t\t\t\t\"last_login_time\": \"2024-10-27 17:27:41\",\n\t\t\t\t\"login_status\": 1,\n\t\t\t\t\"password\": \"81dc9bdb52d04dc20036dbd8313ed055\",\n\t\t\t\t\"update_time\": \"2024-06-18 17:27:03\",\n\t\t\t\t\"username\": \"admin\"\n\t\t\t}\n\t\t],\n\t\t\"total\": 1\n\t}\n}"}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "{{host}}/admin/product/edit": {"post": {"x-target-id": "2a21f2d3fb800b", "x-protocol": "http", "summary": "编辑", "description": "", "tags": ["疗愈/后台/商品"], "x-mark-id": "1", "x-updated-at": "2025-05-06T14:42:36+08:00", "x-updated-user-name": "大王子", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "number", "description": "唯一标识", "x-schema-mock": 1}, "product_name": {"type": "string", "description": "产品名称", "x-schema-mock": "高级瑜伽课程"}, "product_info": {"type": "string", "description": "产品信息", "x-schema-mock": "专业教练指导的高级瑜伽课程，提供全面的健康指导和个性化训练计划"}, "product_time": {"type": "array", "items": {"type": "object", "properties": {"product_name": {"type": "string", "description": "产品时间中的产品名称", "x-schema-mock": "高级瑜伽课程-周末班"}, "start_time": {"type": "number", "description": "产品时间中的开始时间", "x-schema-mock": 1689408000}, "end_time": {"type": "number", "description": "产品时间中的结束时间", "x-schema-mock": 1689418800}, "stock": {"type": "number", "description": "产品时间中的库存", "x-schema-mock": 30}, "type": {"type": "number", "description": "产品时间中的类型", "x-schema-mock": 1}}, "description": "产品时间相关信息"}, "description": "产品时间相关信息"}}}, "example": "{\n  \"product_name\": \"高级瑜伽课程\",\n  \"product_info\": \"专业教练指导的高级瑜伽课程，提供全面的健康指导和个性化训练计划\",\n  \"cover\": \"\",\n  \"product_time\": [\n    {\n      \"product_name\": \"高级瑜伽课程-周一晚班222\",\n      \"start_time\": 1688978400,\n      \"end_time\": 1688985600,\n      \"stock\": 20,\n      \"type\": 1\n    },\n    {\n      \"product_name\": \"高级瑜伽课程-周三晚班222\",\n      \"start_time\": 1689151200,\n      \"end_time\": 1689158400,\n      \"stock\": 15,\n      \"type\": 1\n    },\n    {\n      \"product_name\": \"高级瑜伽课程-周末班\",\n      \"start_time\": 1689408000,\n      \"end_time\": 1689418800,\n      \"stock\": 30,\n      \"type\": 1\n    }\n  ]\n}"}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": "{\n    \"code\": 200,\n    \"msg\": \"成功\",\n    \"data\": {\n        \"id\": 10,\n        \"password\": \"\",\n        \"username\": \"\"\n    }\n}"}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "{{host}}/admin/product/list": {"get": {"x-target-id": "2a21f2d3fb800c", "x-protocol": "http", "summary": "列表", "description": "", "tags": ["疗愈/后台/商品"], "x-mark-id": "1", "x-updated-at": "2025-05-06T14:38:12+08:00", "x-updated-user-name": "大王子", "parameters": [{"name": "page", "in": "query", "description": "", "required": true, "example": "1", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "description": "", "required": true, "example": "10", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": "{\n\t\"code\": 200,\n\t\"msg\": \"成功\",\n\t\"data\": {\n\t\t\"list\": [\n\t\t\t{\n\t\t\t\t\"create_time\": \"2024-06-18 17:27:00\",\n\t\t\t\t\"id\": 1,\n\t\t\t\t\"last_login_time\": \"2024-10-27 17:27:41\",\n\t\t\t\t\"login_status\": 1,\n\t\t\t\t\"password\": \"81dc9bdb52d04dc20036dbd8313ed055\",\n\t\t\t\t\"update_time\": \"2024-06-18 17:27:03\",\n\t\t\t\t\"username\": \"admin\"\n\t\t\t}\n\t\t],\n\t\t\"total\": 1\n\t}\n}"}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "{{host}}/admin/product/del": {"get": {"x-target-id": "2a21f2d3fb800d", "x-protocol": "http", "summary": "删除", "description": "", "tags": ["疗愈/后台/商品"], "x-mark-id": "1", "x-updated-at": "2025-05-06T14:42:32+08:00", "x-updated-user-name": "大王子", "parameters": [{"name": "id", "in": "query", "description": "", "required": true, "example": "30", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": "{\n\t\"code\": 41,\n\t\"msg\": \"成功\",\n\t\"data\": {\n\t\t\"list\": [\n\t\t\t{\n\t\t\t\t\"config_name\": \"123\",\n\t\t\t\t\"config_value\": \"345\",\n\t\t\t\t\"create_time\": null,\n\t\t\t\t\"desc\": \"6666\",\n\t\t\t\t\"id\": 86,\n\t\t\t\t\"update_time\": null\n\t\t\t}\n\t\t],\n\t\t\"total\": 87\n\t}\n}"}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}}, "servers": []}