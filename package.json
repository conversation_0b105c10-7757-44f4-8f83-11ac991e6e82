{"name": "admin2", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:analyze": "vite build --mode analyze", "build:report": "vite build && npx vite-bundle-analyzer dist", "preview": "vite preview", "preview:dist": "vite preview --port 4173", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.9.0", "echarts": "^5.6.0", "element-plus": "^2.10.1", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "terser": "^5.43.1", "vite": "^6.2.4", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-devtools": "^7.7.2"}}