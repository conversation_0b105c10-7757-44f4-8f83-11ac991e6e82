# Vue3 Element Plus Admin

🚀 基于 Vue 3 + Element Plus + Vite 的现代化后台管理系统模板

## ✨ 特性

- 🎯 **Vue 3** - 使用最新的 Vue 3 Composition API
- ⚡ **Vite** - 极速的开发体验和构建性能
- 🎨 **Element Plus** - 优雅的 UI 组件库
- 📱 **响应式设计** - 完美适配各种设备
- 🔐 **权限管理** - 完整的权限控制系统
- 🌍 **CDN 优化** - 生产环境支持 CDN 外部化
- 📦 **Gzip/Brotli** - 多重压缩优化
- 🛠️ **TypeScript Ready** - 完整的类型支持
- 📊 **ECharts 集成** - 丰富的图表组件
- ✏️ **富文本编辑器** - 集成 wangEditor

## 🏗️ 技术栈

- **前端框架**: Vue 3.5.13
- **构建工具**: Vite 6.2.4
- **UI 框架**: Element Plus 2.10.1
- **路由管理**: Vue Router 4.5.1
- **HTTP 客户端**: Axios 1.9.0
- **图表库**: ECharts 5.6.0
- **富文本编辑器**: wangEditor 5.1.23

## 📦 安装使用

### 环境要求

- Node.js >= 16.0.0
- npm >= 7.0.0

### 克隆项目

```bash
git clone <repository-url>
cd admin2
```

### 安装依赖

```bash
npm install
```

### 开发环境

```bash
npm run dev
```

访问 http://localhost:5174

### 生产构建

```bash
# 标准构建
npm run build

# 构建并分析包大小
npm run build:analyze

# 构建并生成分析报告
npm run build:report
```

### 预览构建结果

```bash
npm run preview
```

## 🔧 配置说明

### 环境变量

项目支持多环境配置，通过 `.env` 文件管理：

- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置

主要配置项：

```bash
# API 基础路径
VITE_API_BASE_URL=https://api.yourdomain.com

# 应用标题
VITE_APP_TITLE=Vue3 Element Plus Admin

# 是否启用CDN (生产环境推荐开启)
VITE_USE_CDN=true

# 是否启用压缩 (生产环境推荐开启)
VITE_USE_COMPRESSION=true
```

### CDN 配置

生产环境默认启用 CDN 外部化以下依赖：

- Vue 3.5.13
- Vue Router 4.5.1
- Element Plus 2.10.1
- Axios 1.9.0
- ECharts 5.6.0

### 压缩优化

生产构建支持多重压缩：

- **Gzip 压缩** - 兼容性好，压缩率约 70%
- **Brotli 压缩** - 现代浏览器，压缩率约 80%

## 📁 项目结构

```
src/
├── api/                 # API 接口管理
│   ├── modules/         # 模块化 API
│   ├── request.js       # 请求拦截器
│   └── index.js         # API 统一导出
├── assets/              # 静态资源
├── components/          # 公共组件
│   ├── dialog/          # 对话框组件
│   ├── form/            # 表单组件
│   ├── layout/          # 布局组件
│   └── table/           # 表格组件
├── config/              # 配置文件
├── router/              # 路由配置
│   ├── modules/         # 模块化路由
│   └── index.js         # 路由主文件
├── styles/              # 样式文件
├── utils/               # 工具函数
├── views/               # 页面组件
│   ├── auth/            # 认证相关
│   ├── dashboard/       # 仪表盘
│   ├── system/          # 系统管理
│   └── ...              # 其他业务模块
├── App.vue              # 根组件
└── main.js              # 入口文件
```

## 🚀 性能优化

### 构建优化

1. **代码分割**: 自动分割第三方库和业务代码
2. **Tree Shaking**: 自动移除未使用的代码
3. **压缩优化**: Terser 压缩 + Gzip/Brotli
4. **CDN 外部化**: 大型依赖库使用 CDN 加载

### 运行时优化

1. **路由懒加载**: 页面组件按需加载
2. **组件懒加载**: 大型组件延迟加载
3. **图片优化**: 支持 WebP 格式
4. **缓存策略**: 合理的缓存配置

## 🔐 权限系统

项目内置完整的权限管理系统：

- **路由权限**: 基于角色的路由访问控制
- **按钮权限**: 细粒度的操作权限控制
- **数据权限**: 基于用户的数据访问控制

使用权限指令：

```vue
<template>
  <!-- 按钮权限控制 -->
  <el-button v-permission="'user:create'">新增用户</el-button>
</template>
```

## 📊 开发工具

### 推荐 IDE

- [VSCode](https://code.visualstudio.com/)
- [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (禁用 Vetur)

### 推荐插件

- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier
- Auto Rename Tag
- Bracket Pair Colorizer

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

感谢以下开源项目：

- [Vue.js](https://vuejs.org/)
- [Element Plus](https://element-plus.org/)
- [Vite](https://vitejs.dev/)
- [ECharts](https://echarts.apache.org/)

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 [Issue](../../issues)
- 发送邮件至: <EMAIL>

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
