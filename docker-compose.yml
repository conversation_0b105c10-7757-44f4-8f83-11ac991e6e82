version: '3.8'

services:
  # 前端应用
  admin-frontend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
    depends_on:
      - admin-backend
    restart: unless-stopped
    networks:
      - admin-network

  # 后端服务 (示例)
  admin-backend:
    image: your-backend-image:latest
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**********************************/admin
    depends_on:
      - db
    restart: unless-stopped
    networks:
      - admin-network

  # 数据库 (示例)
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=admin
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - admin-network

  # Redis 缓存 (可选)
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    networks:
      - admin-network

volumes:
  postgres_data:

networks:
  admin-network:
    driver: bridge
