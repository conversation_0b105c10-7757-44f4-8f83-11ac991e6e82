import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import { Plugin as importToCDN } from 'vite-plugin-cdn-import'
import viteCompression from 'vite-plugin-compression'

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')
  const isProduction = command === 'build'
  const useCDN = env.VITE_USE_CDN === 'true' && isProduction
  const useCompression = env.VITE_USE_COMPRESSION === 'true' && isProduction

  return {
    plugins: [
      vue(),
      vueDevTools(),
      // CDN 外部化配置 (仅在生产环境且启用CDN时使用)
      ...(useCDN ? [importToCDN({
        modules: [
          {
            name: 'vue',
            var: 'Vue',
            path: 'https://unpkg.com/vue@3.5.13/dist/vue.global.prod.js'
          },
          {
            name: 'vue-router',
            var: 'VueRouter',
            path: 'https://unpkg.com/vue-router@4.5.1/dist/vue-router.global.prod.js'
          },
          {
            name: 'element-plus',
            var: 'ElementPlus',
            path: 'https://unpkg.com/element-plus@2.10.1/dist/index.full.min.js',
            css: 'https://unpkg.com/element-plus@2.10.1/dist/index.css'
          },
          {
            name: 'axios',
            var: 'axios',
            path: 'https://unpkg.com/axios@1.9.0/dist/axios.min.js'
          },
          {
            name: 'echarts',
            var: 'echarts',
            path: 'https://unpkg.com/echarts@5.6.0/dist/echarts.min.js'
          }
        ]
      })] : []),
      // 压缩插件 (仅在生产环境且启用压缩时使用)
      ...(useCompression ? [
        // Gzip 压缩
        viteCompression({
          algorithm: 'gzip',
          ext: '.gz',
          threshold: 1024,
          deleteOriginFile: false,
          verbose: true
        }),
        // Brotli 压缩 (更好的压缩率)
        viteCompression({
          algorithm: 'brotliCompress',
          ext: '.br',
          threshold: 1024,
          deleteOriginFile: false,
          verbose: true
        })
      ] : [])
    ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    host: '0.0.0.0',
    port: 5174,
    open: false,
    proxy: {
      // API代理配置
      '/api': {
        target: 'http://localhost:8081', // 后端服务地址
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        }
      },
      // 文件上传代理
      '/upload': {
        target: 'http://localhost:8081',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/upload/, '/upload')
      },
      // 静态资源代理
      '/static': {
        target: 'http://localhost:8081',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/static/, '/static')
      }
    }
  },
  build: {
    // 生产环境构建配置
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log']
      }
    },
    rollupOptions: {
      // 外部化依赖，这些依赖将通过CDN加载 (仅在启用CDN时)
      external: useCDN ? ['vue', 'vue-router', 'element-plus', 'axios', 'echarts'] : [],
      output: {
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
        // 手动分包策略
        manualChunks: (id) => {
          // 第三方库
          if (id.includes('node_modules')) {
            // Element Plus 图标单独打包
            if (id.includes('@element-plus/icons-vue')) {
              return 'element-icons'
            }
            // 编辑器相关
            if (id.includes('@wangeditor')) {
              return 'editor'
            }
            // Vue ECharts
            if (id.includes('vue-echarts')) {
              return 'vue-echarts'
            }
            // 其他第三方库
            return 'vendor'
          }
          // 工具类
          if (id.includes('/src/utils/')) {
            return 'utils'
          }
          // API相关
          if (id.includes('/src/api/')) {
            return 'api'
          }
          // 组件
          if (id.includes('/src/components/')) {
            return 'components'
          }
        },
        globals: {
          vue: 'Vue',
          'vue-router': 'VueRouter',
          'element-plus': 'ElementPlus',
          axios: 'axios',
          echarts: 'echarts'
        }
      }
    },
    // 启用压缩
    reportCompressedSize: true,
    // 设置chunk大小警告限制
    chunkSizeWarningLimit: 1000
  },
    define: {
      // 环境变量定义
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: false,
      // 注入环境变量到客户端
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString())
    }
  }
})
