<template>
  <div class="product-list-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">商品管理</h1>
      <div class="header-actions">
        <el-button 
          v-permission="'product:create'"
          type="primary" 
          @click="handleAdd"
        >
          <el-icon><Plus /></el-icon>
          新增商品
        </el-button>
      </div>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchFormData" inline>
        <el-form-item label="商品名称">
          <el-input 
            v-model="searchFormData.name" 
            placeholder="请输入商品名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="商品类型">
          <el-select 
            v-model="searchFormData.type" 
            placeholder="请选择商品类型"
            clearable
            style="width: 150px"
          >
            <el-option label="瑜伽课程" value="yoga" />
            <el-option label="冥想课程" value="meditation" />
            <el-option label="舞蹈课程" value="dance" />
            <el-option label="普拉提" value="pilates" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select 
            v-model="searchFormData.status" 
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="上架" value="1" />
            <el-option label="下架" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <SimpleTable 
        :data="tableData"
        :config="tableConfig"
        :loading="loading"
        :total="total"
        :current-page="currentPage"
        :page-size="pageSize"
        @action="handleTableAction"
        @selection-change="handleSelectionChange"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </el-card>
  </div>
</template>

<script setup>
import { useProductList } from './product_list.js'
import SimpleTable from '@/components/table/SimpleTable.vue'

// 使用商品列表组合式函数
const {
  // 图标组件
  Plus,
  Search,
  Refresh,
  // 响应式数据
  searchFormData,
  tableData,
  tableConfig,
  loading,
  total,
  currentPage,
  pageSize,
  // 方法
  handleAdd,
  handleSearch,
  handleReset,
  handleTableAction,
  handleSelectionChange,
  handlePageChange,
  handleSizeChange
} = useProductList()
</script>

<style scoped src="./product_list.css"></style>
