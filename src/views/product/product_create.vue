<template>
  <div class="product-create-page admin-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">{{ pageTitle }}</h1>
      <div class="header-actions">
        <el-button @click="handleBack" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
    </div>

    <!-- 表单区域 -->
    <el-card class="form-card" shadow="never">
      <SimpleForm 
        ref="formRef"
        :config="formConfig"
        :initial-data="formData"
        @submit="handleFormSubmit"
        @reset="handleFormReset"
        @change="handleFieldChange"
      />
    </el-card>

    <!-- 操作按钮区域 -->
    <div class="form-actions">
      <el-button @click="handleReset">
        <el-icon><Refresh /></el-icon>
        重置
      </el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        <el-icon><Check /></el-icon>
        保存
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { useProductCreate } from './product_create.js'
import SimpleForm from '@/components/form/SimpleForm.vue'

// 使用商品创建组合式函数
const {
  // 图标组件
  ArrowLeft,
  Refresh,
  Check,
  // 表单引用
  formRef,
  // 响应式数据
  submitting,
  pageTitle,
  formData,
  formConfig,
  // 方法
  handleBack,
  handleReset,
  handleSubmit,
  handleFormSubmit,
  handleFormReset,
  handleFieldChange
} = useProductCreate()
</script>

<style scoped src="./product_create.css"></style>
