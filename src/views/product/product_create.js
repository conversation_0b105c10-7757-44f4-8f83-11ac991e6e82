import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Refresh, Check } from '@element-plus/icons-vue'
import { productAPI } from '@/api'

/**
 * 商品创建页面组合式函数
 * @returns {Object} 返回模板需要的所有数据和方法
 */
export function useProductCreate() {
  const router = useRouter()
  const route = useRoute()

  // 表单引用
  const formRef = ref()

  // 页面状态
  const submitting = ref(false)
  const pageTitle = ref('新增商品')

  // 表单数据
  const formData = reactive({})

  // 表单配置
  const formConfig = reactive({
    labelWidth: '120px',
    // 初始数据配置
    initialData: {
      name: '',
      type: '',
      price: '',
      duration: '',
      status: '1',
      description: '',
      image: ''
    },
    fields: [
      {
        type: 'input',
        prop: 'name',
        label: '商品名称',
        placeholder: '请输入商品名称',
        rules: [
          { required: true, message: '请输入商品名称', trigger: 'blur' },
          { min: 2, max: 50, message: '商品名称长度在 2 到 50 个字符', trigger: 'blur' }
        ]
      },
      {
        type: 'select',
        prop: 'type',
        label: '商品类型',
        placeholder: '请选择商品类型',
        options: [
          { label: '瑜伽课程', value: '瑜伽课程' },
          { label: '冥想课程', value: '冥想课程' },
          { label: '舞蹈课程', value: '舞蹈课程' },
          { label: '普拉提', value: '普拉提' }
        ],
        rules: [
          { required: true, message: '请选择商品类型', trigger: 'change' }
        ]
      },
      {
        type: 'input',
        prop: 'price',
        label: '商品价格',
        placeholder: '请输入价格（如：¥299）',
        rules: [
          { required: true, message: '请输入商品价格', trigger: 'blur' }
        ]
      },
      {
        type: 'input',
        prop: 'duration',
        label: '课程时长',
        placeholder: '请输入时长（如：60分钟）',
        rules: [
          { required: true, message: '请输入课程时长', trigger: 'blur' }
        ]
      },
      {
        type: 'input',
        prop: 'image',
        label: '商品图片',
        placeholder: '请输入图片URL',
        rules: [
          { required: true, message: '请输入商品图片URL', trigger: 'blur' },
          { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
        ]
      },
      {
        type: 'radio',
        prop: 'status',
        label: '商品状态',
        options: [
          { label: '上架', value: '1' },
          { label: '下架', value: '0' }
        ],
        rules: [
          { required: true, message: '请选择商品状态', trigger: 'change' }
        ]
      },
      {
        type: 'textarea',
        prop: 'description',
        label: '商品描述',
        placeholder: '请输入商品描述',
        rows: 4,
        rules: [
          { required: true, message: '请输入商品描述', trigger: 'blur' },
          { max: 500, message: '商品描述不能超过500个字符', trigger: 'blur' }
        ]
      }
    ],
    actions: [
      { type: 'submit', label: '保存', style: 'primary' },
      { type: 'reset', label: '重置', style: 'default' }
    ]
  })

  // 初始化表单数据
  Object.assign(formData, formConfig.initialData)

  // 返回上一页
  const handleBack = () => {
    router.back()
  }

  // 重置表单
  const handleReset = () => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
    // 从配置中重置数据
    Object.assign(formData, formConfig.initialData)
    ElMessage.info('表单已重置')
  }

  // 提交表单
  const handleSubmit = async () => {
    try {
      // 表单验证
      await formRef.value?.validate()

      submitting.value = true

      // 准备提交数据
      const productData = { ...formData }

      console.log('创建商品数据:', productData)

      // 调用API创建商品
      await productAPI.createProduct(productData)

      ElMessage.success('商品创建成功！')

      // 跳转回商品列表
      setTimeout(() => {
        router.push('/admin/product-list')
      }, 1000)

    } catch (error) {
      if (error !== 'cancel') {
        console.error('创建商品失败:', error)
        ElMessage.error('创建商品失败，请检查表单信息')
      }
    } finally {
      submitting.value = false
    }
  }

  // 表单提交处理（由SimpleForm触发）
  const handleFormSubmit = (data) => {
    Object.assign(formData, data)
    handleSubmit()
  }

  // 表单重置处理（由SimpleForm触发）
  const handleFormReset = () => {
    handleReset()
  }

  // 字段变化处理
  const handleFieldChange = (prop, value) => {
    console.log(`[ProductCreate] Field change: ${prop}`, value)

    // 更新表单数据
    if (typeof prop === 'object') {
      // 如果传入的是对象，批量更新
      Object.assign(formData, prop)
    } else {
      // 单个字段更新
      formData[prop] = value
    }
  }

  // 返回模板需要的所有数据和方法
  return {
    // 图标组件
    ArrowLeft,
    Refresh,
    Check,
    // 表单引用
    formRef,
    // 响应式数据
    submitting,
    pageTitle,
    formData,
    formConfig,
    // 方法
    handleBack,
    handleReset,
    handleSubmit,
    handleFormSubmit,
    handleFormReset,
    handleFieldChange
  }
}
