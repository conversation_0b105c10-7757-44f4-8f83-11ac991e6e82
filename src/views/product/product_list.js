import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { productAPI } from '@/api'

/**
 * 商品列表页面组合式函数
 * @returns {Object} 返回模板需要的所有数据和方法
 */
export function useProductList() {
  const router = useRouter()

  // 分页状态
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const loading = ref(false)

  // 搜索表单数据
  const searchFormData = reactive({
    name: '',
    type: '',
    status: ''
  })

  // 表格配置
  const tableConfig = {
    columns: [
      { prop: 'id', label: 'ID', width: 80 },
      { 
        prop: 'image',
        label: '商品图片',
        type: 'image',
        width: 100,
        imageStyle: { width: '60px', height: '60px', borderRadius: '8px' }
      },
      { prop: 'name', label: '商品名称', width: 200 },
      { prop: 'type', label: '商品类型', width: 120 },
      { prop: 'price', label: '价格', width: 100 },
      { prop: 'duration', label: '时长', width: 100 },
      { 
        prop: 'status', 
        label: '状态',
        type: 'tag',
        width: 100,
        tagMap: {
          '1': { type: 'success', text: '上架' },
          '0': { type: 'danger', text: '下架' }
        }
      },
      { prop: 'description', label: '商品描述', width: 200 },
      { prop: 'create_time', label: '创建时间', type: 'datetime', width: 160 },
      { prop: 'update_time', label: '更新时间', type: 'datetime', width: 160 }
    ],
    actions: [
      { type: 'view', label: '查看', style: 'text' },
      { type: 'edit', label: '编辑', style: 'primary' },
      { type: 'delete', label: '删除', style: 'danger' }
    ],
    pagination: true,
    selection: true
  }

  // 表格数据
  const tableData = ref([])

  // 模拟商品数据
  const mockProducts = [
    {
      id: 1,
      name: '高级瑜伽课程',
      type: '瑜伽课程',
      price: '¥299',
      duration: '60分钟',
      status: '1',
      description: '专业瑜伽导师指导，适合有一定基础的学员',
      image: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=200&h=200&fit=crop',
      create_time: '2024-01-01 10:00:00',
      update_time: '2024-06-19 10:30:00'
    },
    {
      id: 2,
      name: '初级瑜伽课程',
      type: '瑜伽课程',
      price: '¥199',
      duration: '45分钟',
      status: '1',
      description: '零基础瑜伽入门课程，轻松开始瑜伽之旅',
      image: 'https://images.unsplash.com/photo-1506629905607-c7b0b5e8e8e8?w=200&h=200&fit=crop',
      create_time: '2024-01-02 10:00:00',
      update_time: '2024-06-19 10:30:00'
    },
    {
      id: 3,
      name: '冥想课程',
      type: '冥想课程',
      price: '¥159',
      duration: '30分钟',
      status: '1',
      description: '放松身心，缓解压力的冥想练习',
      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop',
      create_time: '2024-01-03 10:00:00',
      update_time: '2024-06-19 10:30:00'
    },
    {
      id: 4,
      name: '普拉提课程',
      type: '普拉提',
      price: '¥259',
      duration: '50分钟',
      status: '1',
      description: '核心力量训练，塑造完美身形',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=200&h=200&fit=crop',
      create_time: '2024-01-04 10:00:00',
      update_time: '2024-06-19 10:30:00'
    },
    {
      id: 5,
      name: '舞蹈课程',
      type: '舞蹈课程',
      price: '¥229',
      duration: '60分钟',
      status: '0',
      description: '优美舞蹈动作，提升气质与协调性',
      image: 'https://images.unsplash.com/photo-1508700115892-45ecd05ae2ad?w=200&h=200&fit=crop',
      create_time: '2024-01-05 10:00:00',
      update_time: '2024-06-19 10:30:00'
    },
    {
      id: 6,
      name: '高温瑜伽',
      type: '瑜伽课程',
      price: '¥359',
      duration: '75分钟',
      status: '1',
      description: '在高温环境中练习瑜伽，加速排毒',
      image: 'https://images.unsplash.com/photo-1599901860904-17e6ed7083a0?w=200&h=200&fit=crop',
      create_time: '2024-01-06 10:00:00',
      update_time: '2024-06-19 10:30:00'
    }
  ]

  // 获取商品列表
  const fetchProductList = async () => {
    loading.value = true
    try {
      const params = {
        page: currentPage.value,
        limit: pageSize.value,
        ...searchFormData
      }

      const response = await productAPI.getProductList(params)

      if (response && response.data) {
        tableData.value = response.data.list || []
        total.value = response.data.total || 0
      } else {
        tableData.value = response.list || response || []
        total.value = response.total || tableData.value.length
      }

    } catch (error) {
      console.error('获取商品列表失败:', error)
      ElMessage.error('获取商品列表失败，使用模拟数据')

      // 如果API失败，使用模拟数据作为备用
      let filteredData = [...mockProducts]

      if (searchFormData.name) {
        filteredData = filteredData.filter(product =>
          product.name.includes(searchFormData.name)
        )
      }

      if (searchFormData.type) {
        filteredData = filteredData.filter(product =>
          product.type === searchFormData.type
        )
      }

      if (searchFormData.status) {
        filteredData = filteredData.filter(product =>
          product.status === searchFormData.status
        )
      }

      // 模拟分页
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value

      tableData.value = filteredData.slice(start, end)
      total.value = filteredData.length
    } finally {
      loading.value = false
    }
  }

  // 搜索商品
  const handleSearch = () => {
    currentPage.value = 1
    fetchProductList()
    ElMessage.success('搜索完成')
  }

  // 重置搜索
  const handleReset = () => {
    Object.assign(searchFormData, {
      name: '',
      type: '',
      status: ''
    })
    currentPage.value = 1
    fetchProductList()
    ElMessage.info('已重置搜索条件')
  }

  // 新增商品
  const handleAdd = () => {
    router.push('/admin/product-list/product-create')
  }

  // 表格操作处理
  const handleTableAction = ({ type, row }) => {
    switch (type) {
      case 'view':
        ElMessage.info(`查看商品：${row.name}`)
        // router.push(`/admin/product-edit/${row.id}?mode=view`)
        break
      case 'edit':
        ElMessage.success(`编辑商品：${row.name}`)
        // router.push(`/admin/product-edit/${row.id}`)
        break
      case 'delete':
        handleDelete(row)
        break
      default:
        ElMessage.warning(`未知操作：${type}`)
    }
  }

  // 删除商品
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除商品 "${row.name}" 吗？`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      // 模拟删除API
      await new Promise(resolve => setTimeout(resolve, 500))
      
      ElMessage.success(`商品 "${row.name}" 删除成功`)
      fetchProductList()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 选择变化
  const handleSelectionChange = (selection) => {
    console.log('选中的商品：', selection)
    if (selection.length > 0) {
      ElMessage.info(`已选中 ${selection.length} 个商品`)
    }
  }

  // 分页改变
  const handlePageChange = (page) => {
    currentPage.value = page
    fetchProductList()
  }

  // 页面大小改变
  const handleSizeChange = (size) => {
    pageSize.value = size
    currentPage.value = 1
    fetchProductList()
  }

  // 页面加载时获取数据
  onMounted(() => {
    fetchProductList()
  })

  // 返回模板需要的所有数据和方法
  return {
    // 图标组件
    Plus,
    Search,
    Refresh,
    // 响应式数据
    searchFormData,
    tableData,
    tableConfig,
    loading,
    total,
    currentPage,
    pageSize,
    // 方法
    handleAdd,
    handleSearch,
    handleReset,
    handleTableAction,
    handleSelectionChange,
    handlePageChange,
    handleSizeChange
  }
}
