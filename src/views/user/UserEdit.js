import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Refresh, Check } from '@element-plus/icons-vue'

/**
 * 用户编辑页面组合式函数
 * @returns {Object} 返回模板需要的所有数据和方法
 */
export function useUserEdit() {
  const router = useRouter()
  const route = useRoute()
  const formRef = ref()

  // 页面状态
  const isEditMode = computed(() => !!route.params.id)
  const isViewMode = computed(() => route.query.mode === 'view')
  const submitting = ref(false)

  // 页面标题
  const pageTitle = computed(() => {
    if (isViewMode.value) return '查看用户'
    return isEditMode.value ? '编辑用户' : '新增用户'
  })

  const pageDescription = computed(() => {
    if (isViewMode.value) return '查看用户详细信息'
    return isEditMode.value ? '修改用户信息' : '创建新的用户账号'
  })

  // 表单数据
  const formData = reactive({
    id: '',
    username: '',
    realName: '',
    email: '',
    phone: '',
    role: 'user',
    status: '1',
    avatar: '',
    department: [],
    birthDate: '',
    gender: '',
    address: '',
    description: '',
    permissions: [],
    lastLoginTime: '',
    createTime: ''
  })

  // 表单配置
  const formConfig = reactive({
    labelWidth: '120px',
    fields: [
      {
        type: 'input',
        prop: 'username',
        label: '用户名',
        placeholder: '请输入用户名',
        rules: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        disabled: isViewMode.value || isEditMode.value
      },
      {
        type: 'input',
        prop: 'realName',
        label: '真实姓名',
        placeholder: '请输入真实姓名',
        rules: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' }
        ],
        disabled: isViewMode.value
      },
      {
        type: 'input',
        prop: 'email',
        label: '邮箱',
        placeholder: '请输入邮箱地址',
        rules: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        disabled: isViewMode.value
      },
      {
        type: 'input',
        prop: 'phone',
        label: '手机号',
        placeholder: '请输入手机号码',
        rules: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        disabled: isViewMode.value
      },
      {
        type: 'select',
        prop: 'role',
        label: '用户角色',
        placeholder: '请选择用户角色',
        options: [
          { label: '管理员', value: 'admin' },
          { label: '编辑员', value: 'editor' },
          { label: '普通用户', value: 'user' }
        ],
        rules: [
          { required: true, message: '请选择用户角色', trigger: 'change' }
        ],
        disabled: isViewMode.value
      },
      {
        type: 'radio',
        prop: 'status',
        label: '账号状态',
        options: [
          { label: '启用', value: '1' },
          { label: '禁用', value: '0' }
        ],
        rules: [
          { required: true, message: '请选择账号状态', trigger: 'change' }
        ],
        disabled: isViewMode.value
      },
      {
        type: 'cascader',
        prop: 'department',
        label: '所属部门',
        placeholder: '请选择所属部门',
        options: [
          {
            label: '技术部',
            value: 'tech',
            children: [
              { label: '前端开发', value: 'frontend' },
              { label: '后端开发', value: 'backend' },
              { label: '移动开发', value: 'mobile' },
              { label: '测试工程', value: 'test' }
            ]
          },
          {
            label: '产品部',
            value: 'product',
            children: [
              { label: '产品经理', value: 'pm' },
              { label: 'UI设计', value: 'ui' },
              { label: '交互设计', value: 'ux' }
            ]
          },
          {
            label: '运营部',
            value: 'operation',
            children: [
              { label: '内容运营', value: 'content' },
              { label: '用户运营', value: 'user' },
              { label: '数据分析', value: 'data' }
            ]
          }
        ],
        disabled: isViewMode.value
      },
      {
        type: 'date',
        prop: 'birthDate',
        label: '出生日期',
        placeholder: '请选择出生日期',
        disabled: isViewMode.value
      },
      {
        type: 'radio',
        prop: 'gender',
        label: '性别',
        options: [
          { label: '男', value: 'male' },
          { label: '女', value: 'female' },
          { label: '保密', value: 'secret' }
        ],
        disabled: isViewMode.value
      },
      {
        type: 'textarea',
        prop: 'address',
        label: '联系地址',
        placeholder: '请输入联系地址',
        rows: 3,
        disabled: isViewMode.value
      },
      {
        type: 'richtext',
        prop: 'description',
        label: '个人简介',
        placeholder: '请输入个人简介...',
        disabled: isViewMode.value
      },
      {
        type: 'checkbox',
        prop: 'permissions',
        label: '权限设置',
        options: [
          { label: '用户管理', value: 'user_manage' },
          { label: '角色管理', value: 'role_manage' },
          { label: '菜单管理', value: 'menu_manage' },
          { label: '系统设置', value: 'system_setting' },
          { label: '日志查看', value: 'log_view' }
        ],
        disabled: isViewMode.value || formData.role === 'user'
      },
      {
        type: 'upload',
        prop: 'avatar',
        label: '头像上传',
        accept: 'image/*',
        limit: 1,
        listType: 'picture-card',
        disabled: isViewMode.value
      }
    ],
    actions: [] // 移除内置操作按钮，使用外部按钮
  })

  // 模拟用户数据
  const mockUserData = {
    1: {
      id: '1',
      username: 'admin',
      realName: '管理员',
      email: '<EMAIL>',
      phone: '13800138001',
      role: 'admin',
      status: '1',
      department: ['tech', 'frontend'],
      birthDate: '1990-01-01',
      gender: 'male',
      address: '北京市朝阳区某某街道123号',
      description: '<p>这是一个系统管理员账号，负责整个系统的管理和维护工作。</p>',
      permissions: ['user_manage', 'role_manage', 'menu_manage', 'system_setting', 'log_view'],
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      createTime: '2024-01-01 10:00:00',
      lastLoginTime: '2024-06-09 10:30:00'
    }
  }

  // 获取用户信息
  const fetchUserInfo = async (id) => {
    try {
      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const userData = mockUserData[id]
      if (userData) {
        Object.assign(formData, userData)
      } else {
        ElMessage.error('用户不存在')
        router.push('/admin/user-list')
      }
    } catch (error) {
      ElMessage.error('获取用户信息失败')
      console.error(error)
    }
  }

  // 返回上一级
  const handleBack = () => {
    router.back()
  }

  // 重置表单
  const handleReset = () => {
    formRef.value?.resetFields()
  }

  // 提交表单
  const handleSubmit = () => {
    formRef.value?.validate()
  }

  // 表单提交处理
  const handleFormSubmit = async (data) => {
    submitting.value = true
    try {
      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      if (isEditMode.value) {
        ElMessage.success('用户信息更新成功')
      } else {
        ElMessage.success('用户创建成功')
      }
      
      // 返回列表页
      router.push('/admin/user-list')
    } catch (error) {
      ElMessage.error(isEditMode.value ? '更新失败' : '创建失败')
      console.error(error)
    } finally {
      submitting.value = false
    }
  }

  // 表单重置处理
  const handleFormReset = () => {
    ElMessage.info('表单已重置')
  }

  // 字段变化处理
  const handleFieldChange = (prop, value) => {
    // 当角色改变时，处理权限设置
    if (prop === 'role') {
      if (value === 'user') {
        formData.permissions = []
      }
      // 更新权限字段的禁用状态
      const permissionField = formConfig.fields.find(field => field.prop === 'permissions')
      if (permissionField) {
        permissionField.disabled = isViewMode.value || value === 'user'
      }
    }
  }

  // 页面加载时获取数据
  onMounted(() => {
    if (isEditMode.value) {
      fetchUserInfo(route.params.id)
    }
  })

  // 返回模板需要的所有数据和方法
  return {
    // 图标组件
    ArrowLeft,
    Refresh,
    Check,
    // 表单引用
    formRef,
    // 响应式数据
    isEditMode,
    isViewMode,
    submitting,
    pageTitle,
    pageDescription,
    formData,
    formConfig,
    // 方法
    handleBack,
    handleReset,
    handleSubmit,
    handleFormSubmit,
    handleFormReset,
    handleFieldChange
  }
}
