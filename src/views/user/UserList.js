import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'

/**
 * 用户列表页面组合式函数
 * @returns {Object} 返回模板需要的所有数据和方法
 */
export function useUserList() {
  const router = useRouter()

  // 搜索表单
  const searchForm = reactive({
    username: '',
    email: '',
    status: ''
  })

  // 表格数据
  const tableData = ref([])
  const loading = ref(false)

  // 分页信息
  const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0
  })

  // 表格配置
  const tableConfig = reactive({
    columns: [
      {
        prop: 'id',
        label: 'ID',
        width: 80
      },
      {
        prop: 'avatar',
        label: '头像',
        width: 80,
        type: 'image',
        imageStyle: {
          width: '40px',
          height: '40px',
          borderRadius: '50%'
        }
      },
      {
        prop: 'username',
        label: '用户名',
        width: 120
      },
      {
        prop: 'realName',
        label: '真实姓名',
        width: 120
      },
      {
        prop: 'email',
        label: '邮箱',
        width: 200
      },
      {
        prop: 'phone',
        label: '手机号',
        width: 130
      },
      {
        prop: 'role',
        label: '角色',
        width: 100,
        type: 'tag',
        tagMap: {
          'admin': { type: 'danger', text: '管理员' },
          'user': { type: 'primary', text: '普通用户' },
          'editor': { type: 'warning', text: '编辑员' }
        }
      },
      {
        prop: 'status',
        label: '状态',
        width: 100,
        type: 'tag',
        tagMap: {
          '1': { type: 'success', text: '启用' },
          '0': { type: 'danger', text: '禁用' }
        }
      },
      {
        prop: 'createTime',
        label: '创建时间',
        width: 160,
        type: 'datetime'
      },
      {
        prop: 'lastLoginTime',
        label: '最后登录',
        width: 160,
        type: 'datetime'
      }
    ],
    actions: [
      {
        type: 'view',
        label: '查看',
        style: 'text',
        icon: 'View'
      },
      {
        type: 'edit',
        label: '编辑',
        style: 'primary',
        icon: 'Edit',
        permission: 'user:edit'
      },
      {
        type: 'delete',
        label: '删除',
        style: 'danger',
        icon: 'Delete',
        confirm: true,
        confirmText: '确定要删除该用户吗？',
        permission: 'user:delete'
      }
    ],
    pagination: {
      show: true,
      currentPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      total: pagination.total,
      pageSizes: [10, 20, 50, 100]
    },
    selection: true,
    border: true,
    stripe: true
  })

  // 模拟用户数据
  const mockUsers = [
    {
      id: 1,
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      username: 'admin',
      realName: '管理员',
      email: '<EMAIL>',
      phone: '13800138001',
      role: 'admin',
      status: '1',
      createTime: '2024-01-01 10:00:00',
      lastLoginTime: '2024-06-09 10:30:00'
    },
    {
      id: 2,
      avatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
      username: 'editor01',
      realName: '张编辑',
      email: '<EMAIL>',
      phone: '13800138002',
      role: 'editor',
      status: '1',
      createTime: '2024-01-15 14:20:00',
      lastLoginTime: '2024-06-08 16:45:00'
    },
    {
      id: 3,
      avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
      username: 'user001',
      realName: '李用户',
      email: '<EMAIL>',
      phone: '13800138003',
      role: 'user',
      status: '1',
      createTime: '2024-02-01 09:15:00',
      lastLoginTime: '2024-06-07 11:20:00'
    },
    {
      id: 4,
      avatar: 'https://cube.elemecdn.com/6/94/4d3ea53c9c551ba2f93c97b0deb3a5png.png',
      username: 'testuser',
      realName: '王测试',
      email: '<EMAIL>',
      phone: '13800138004',
      role: 'user',
      status: '0',
      createTime: '2024-03-10 16:30:00',
      lastLoginTime: '2024-05-20 10:15:00'
    },
    {
      id: 5,
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      username: 'designer',
      realName: '赵设计',
      email: '<EMAIL>',
      phone: '13800138005',
      role: 'editor',
      status: '1',
      createTime: '2024-04-05 11:45:00',
      lastLoginTime: '2024-06-09 09:30:00'
    }
  ]

  // 获取用户列表
  const fetchUserList = async () => {
    loading.value = true
    try {
      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 模拟搜索过滤
      let filteredData = [...mockUsers]
      
      if (searchForm.username) {
        filteredData = filteredData.filter(user => 
          user.username.includes(searchForm.username) || 
          user.realName.includes(searchForm.username)
        )
      }
      
      if (searchForm.email) {
        filteredData = filteredData.filter(user => 
          user.email.includes(searchForm.email)
        )
      }
      
      if (searchForm.status) {
        filteredData = filteredData.filter(user => 
          user.status === searchForm.status
        )
      }
      
      // 模拟分页
      const start = (pagination.currentPage - 1) * pagination.pageSize
      const end = start + pagination.pageSize
      
      tableData.value = filteredData.slice(start, end)
      pagination.total = filteredData.length
      
      // 更新表格配置中的分页信息
      tableConfig.pagination.currentPage = pagination.currentPage
      tableConfig.pagination.pageSize = pagination.pageSize
      tableConfig.pagination.total = pagination.total
      
    } catch (error) {
      ElMessage.error('获取用户列表失败')
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  // 搜索用户
  const handleSearch = () => {
    pagination.currentPage = 1
    fetchUserList()
  }

  // 重置搜索
  const handleReset = () => {
    Object.assign(searchForm, {
      username: '',
      email: '',
      status: ''
    })
    pagination.currentPage = 1
    fetchUserList()
  }

  // 新增用户
  const handleAdd = () => {
    router.push('/admin/user-edit')
  }

  // 查看用户
  const handleView = (row) => {
    router.push(`/admin/user-edit/${row.id}?mode=view`)
  }

  // 编辑用户
  const handleEdit = (row) => {
    router.push(`/admin/user-edit/${row.id}`)
  }

  // 删除用户
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除用户 "${row.realName}" 吗？`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      // 模拟删除API
      await new Promise(resolve => setTimeout(resolve, 300))
      
      ElMessage.success('删除成功')
      fetchUserList()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 分页改变
  const handlePageChange = (page) => {
    pagination.currentPage = page
    fetchUserList()
  }

  // 页面大小改变
  const handleSizeChange = (size) => {
    pagination.pageSize = size
    pagination.currentPage = 1
    fetchUserList()
  }



  // 页面加载时获取数据
  onMounted(() => {
    fetchUserList()
  })

  // 返回模板需要的所有数据和方法
  return {
    // 图标组件
    Plus,
    Search,
    Refresh,
    // 响应式数据
    searchForm,
    tableData,
    loading,
    tableConfig,
    // 方法
    handleSearch,
    handleReset,
    handleAdd,
    handleView,
    handleEdit,
    handleDelete,
    handlePageChange,
    handleSizeChange
  }
}
