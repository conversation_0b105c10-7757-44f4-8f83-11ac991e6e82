import { ref, reactive, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Refresh, Check } from '@element-plus/icons-vue'

/**
 * 用户创建页面组合式函数（简化配置版本）
 * @returns {Object} 返回模板需要的所有数据和方法
 */
export function useUserCreateV2() {
  const router = useRouter()

  // 表单引用
  const formRef = ref()

  // 页面状态
  const submitting = ref(false)
  const pageTitle = ref('新增用户（简化配置）')

  // 统一事件处理函数
  const handleUploadEvent = (eventType, prop, ...args) => {
    console.log(`[UserCreate] Upload event: ${eventType} for ${prop}`, args)

    switch (eventType) {
      case 'success':
        const [response, file] = args
        console.log(`[UserCreate] ${prop} 上传成功:`, response, file)
        ElMessage.success(`${prop === 'avatar' ? '头像' : '图片'}上传成功！`)
        break

      case 'error':
        const [error] = args
        console.error(`[UserCreate] ${prop} 上传失败:`, error)
        ElMessage.error(`${prop === 'avatar' ? '头像' : '图片'}上传失败，请重试`)
        break

      case 'remove':
        const [removeFile] = args
        console.log(`[UserCreate] 删除 ${prop} 文件:`, removeFile)
        ElMessage.info(`${prop === 'avatar' ? '头像' : '图片'}已删除`)
        break

      case 'beforeUpload':
        const [beforeFile] = args
        console.log(`[UserCreate] 准备上传 ${prop}:`, beforeFile)

        // 检查文件类型
        const isImage = beforeFile.type.startsWith('image/')
        if (!isImage) {
          ElMessage.error('只能上传图片文件！')
          return false
        }

        // 检查文件大小（限制为2MB）
        const isLt2M = beforeFile.size / 1024 / 1024 < 2
        if (!isLt2M) {
          ElMessage.error('图片文件大小不能超过 2MB！')
          return false
        }

        ElMessage.info(`开始处理${prop === 'avatar' ? '头像' : '图片'}...`)
        return true

      case 'preview':
        const [previewFile] = args
        console.log(`[UserCreate] 预览 ${prop}:`, previewFile)
        // 预览功能现在由 SimpleForm 的弹窗处理，这里不需要额外操作
        break

      case 'progress':
        const [event, progressFile] = args
        console.log(`[UserCreate] ${prop} 上传进度:`, Math.round(event.percent), '%', progressFile)
        break

      case 'change':
        const [changeFile, fileList] = args
        console.log(`[UserCreate] ${prop} 文件状态改变:`, changeFile, fileList)

        if (prop === 'photos') {
          // 本地文件处理模式
          ElMessage.info(`相册图片 ${changeFile.name} 已添加到本地预览`)
        } else {
          // 服务器上传模式
          ElMessage.info(`文件 ${changeFile.name} 状态: ${changeFile.status}`)
        }
        break

      case 'exceed':
        const [files, currentFileList] = args
        console.log(`[UserCreate] ${prop} 超出限制:`, files, currentFileList)

        const fieldConfig = formConfig.fields.find(field => field.prop === prop)
        const limit = fieldConfig?.limit || 0
        const currentCount = currentFileList?.length || 0
        const newCount = files?.length || 0

        ElMessage.warning(
          `${prop === 'avatar' ? '头像' : '相册'}上传数量限制为 ${limit} 个，` +
          `当前已有 ${currentCount} 个，尝试添加 ${newCount} 个，已达到上限！`
        )
        break

      default:
        console.log(`[UserCreate] 未处理的事件类型: ${eventType}`)
    }
  }

  // 表单数据 - 从 formConfig.initialData 初始化
  const formData = reactive({})

  // 表单配置 - 参考 UserEdit.js 的完整配置
  const formConfig = reactive({
    labelWidth: '120px',
    // 初始数据配置
    initialData: {
      username: '',
      realName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      role: 'user',
      status: '1',
      avatar: [],
      photos: [],
      department: [],
      birthDate: '',
      gender: '',
      address: '',
      description: '',
      permissions: []
    },
    fields: [
      {
        type: 'input',
        prop: 'username',
        label: '用户名',
        placeholder: '请输入用户名',
        rules: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
        ]
      },
      {
        type: 'input',
        prop: 'realName',
        label: '真实姓名',
        placeholder: '请输入真实姓名',
        rules: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' }
        ]
      },
      {
        type: 'input',
        prop: 'email',
        label: '邮箱',
        placeholder: '请输入邮箱地址',
        rules: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      },
      {
        type: 'input',
        prop: 'phone',
        label: '手机号',
        placeholder: '请输入手机号码',
        rules: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      },
      {
        type: 'password',
        prop: 'password',
        label: '密码',
        placeholder: '请输入密码',
        rules: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
        ]
      },
      {
        type: 'password',
        prop: 'confirmPassword',
        label: '确认密码',
        placeholder: '请再次输入密码',
        rules: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value !== formData.password) {
                callback(new Error('两次输入密码不一致'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },
      {
        type: 'select',
        prop: 'role',
        label: '用户角色',
        placeholder: '请选择用户角色',
        options: [
          { label: '管理员', value: 'admin' },
          { label: '编辑员', value: 'editor' },
          { label: '普通用户', value: 'user' }
        ],
        rules: [
          { required: true, message: '请选择用户角色', trigger: 'change' }
        ]
      },
      {
        type: 'radio',
        prop: 'status',
        label: '账号状态',
        options: [
          { label: '启用', value: '1' },
          { label: '禁用', value: '0' }
        ],
        rules: [
          { required: true, message: '请选择账号状态', trigger: 'change' }
        ]
      },
      {
        type: 'cascader',
        prop: 'department',
        label: '所属部门',
        placeholder: '请选择所属部门',
        options: [
          {
            label: '技术部',
            value: 'tech',
            children: [
              { label: '前端开发', value: 'frontend' },
              { label: '后端开发', value: 'backend' },
              { label: '移动开发', value: 'mobile' },
              { label: '测试工程', value: 'test' }
            ]
          },
          {
            label: '产品部',
            value: 'product',
            children: [
              { label: '产品经理', value: 'pm' },
              { label: 'UI设计', value: 'ui' },
              { label: '交互设计', value: 'ux' }
            ]
          },
          {
            label: '运营部',
            value: 'operation',
            children: [
              { label: '内容运营', value: 'content' },
              { label: '用户运营', value: 'user' },
              { label: '数据分析', value: 'data' }
            ]
          }
        ]
      },
      {
        type: 'date',
        prop: 'birthDate',
        label: '出生日期',
        placeholder: '请选择出生日期'
      },
      {
        type: 'radio',
        prop: 'gender',
        label: '性别',
        options: [
          { label: '男', value: 'male' },
          { label: '女', value: 'female' },
          { label: '保密', value: 'secret' }
        ]
      },
      {
        type: 'textarea',
        prop: 'address',
        label: '联系地址',
        placeholder: '请输入联系地址',
        rows: 3
      },
      {
        type: 'richtext',
        prop: 'description',
        label: '个人简介',
        placeholder: '请输入个人简介...'
      },
      {
        type: 'checkbox',
        prop: 'permissions',
        label: '权限设置',
        options: [
          { label: '用户管理', value: 'user_manage' },
          { label: '角色管理', value: 'role_manage' },
          { label: '菜单管理', value: 'menu_manage' },
          { label: '系统设置', value: 'system_setting' },
          { label: '日志查看', value: 'log_view' }
        ],
        disabled: computed(() => formData.role === 'user')
      },
      {
        type: 'upload',
        prop: 'avatar',
        label: '头像上传（服务器上传）',
        accept: 'image/*',
        action: '/api/upload',
        limit: 2,
        listType: 'picture-card',
        // 统一事件处理
        onEvent: handleUploadEvent,
        // 也可以单独配置 exceed 回调（测试兼容性）
        onExceed: (files, fileList) => {
          console.log('[UserCreate] 单独的 onExceed 回调:', files, fileList)
          ElMessage.error('头像只能上传1张图片！')
        }
      },
      {
        type: 'upload',
        prop: 'photos',
        label: '相册上传（本地预览）',
        accept: 'image/*',
        // 不设置 action，实现本地文件处理
        limit: 3,
        listType: 'picture-card',
        multiple: true,
        // 统一事件处理
        onEvent: handleUploadEvent
      }
    ],
    actions: [
      { type: 'submit', label: '保存', style: 'primary' },
      { type: 'reset', label: '重置', style: 'default' }
    ]
  })

  // 初始化表单数据
  Object.assign(formData, formConfig.initialData)

  // 监控上传字段的变化（调试用）
  watch(() => [formData.avatar, formData.photos], ([avatar, photos]) => {
    console.log('[UserCreate] Upload fields changed:', {
      avatar: { count: Array.isArray(avatar) ? avatar.length : 'not array', data: avatar },
      photos: { count: Array.isArray(photos) ? photos.length : 'not array', data: photos }
    })
  }, { deep: true })

  // 返回上一页
  const handleBack = () => {
    router.back()
  }

  // 重置表单
  const handleReset = () => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
    // 从配置中重置数据
    Object.assign(formData, formConfig.initialData)
    ElMessage.info('表单已重置')
  }

  // 提交表单
  const handleSubmit = async () => {
    try {
      // 表单验证
      await formRef.value?.validate()
      
      submitting.value = true
      
      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // 模拟创建用户
      const userData = { ...formData }
      delete userData.confirmPassword // 移除确认密码字段
      
      console.log('创建用户数据:', userData)
      
      ElMessage.success('用户创建成功！')
      
      // 跳转回用户列表
      // setTimeout(() => {
      //   router.push('/admin/user-list-v2')
      // }, 1000)
      
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('创建用户失败，请检查表单信息')
        console.error('创建用户失败:', error)
      }
    } finally {
      submitting.value = false
    }
  }

  // 表单提交处理（由SimpleForm触发）
  const handleFormSubmit = (data) => {
    Object.assign(formData, data)
    handleSubmit()
  }

  // 表单重置处理（由SimpleForm触发）
  const handleFormReset = () => {
    handleReset()
  }

  // 字段变化处理
  const handleFieldChange = (prop, value) => {
    console.log(`[UserCreate] Field change: ${prop}`, value)

    // 更新表单数据
    if (typeof prop === 'object') {
      // 如果传入的是对象，批量更新
      Object.assign(formData, prop)

      // 如果密码改变了，需要重新验证确认密码
      if (prop.password !== undefined && formRef.value) {
        formRef.value.clearValidate('confirmPassword')
      }
    } else {
      // 单个字段更新
      formData[prop] = value

      // 特别关注上传字段的变化
      if (prop === 'avatar' || prop === 'photos') {
        console.log(`[UserCreate] Upload field ${prop} updated:`, value, `length: ${Array.isArray(value) ? value.length : 'not array'}`)
      }

      // 当角色改变时，处理权限设置
      if (prop === 'role') {
        if (value === 'user') {
          formData.permissions = []
        }
        // 更新权限字段的禁用状态
        const permissionField = formConfig.fields.find(field => field.prop === 'permissions')
        if (permissionField) {
          permissionField.disabled = computed(() => value === 'user')
        }
      }

      // 如果密码改变了，需要重新验证确认密码
      if (prop === 'password' && formRef.value) {
        formRef.value.clearValidate('confirmPassword')
      }
    }
  }

  // 返回模板需要的所有数据和方法
  return {
    // 图标组件
    ArrowLeft,
    Refresh,
    Check,
    // 表单引用
    formRef,
    // 响应式数据
    submitting,
    pageTitle,
    formData,
    formConfig,
    // 方法
    handleBack,
    handleReset,
    handleSubmit,
    handleFormSubmit,
    handleFormReset,
    handleFieldChange
  }
}
