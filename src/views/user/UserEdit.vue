<template>
  <div class="user-edit-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">{{ pageTitle }}</h1>
      <div class="header-actions">
        <el-button @click="handleBack" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
    </div>

    <!-- 表单区域 -->
    <el-card class="form-card" shadow="never">
      <ConfigurableForm 
        ref="formRef"
        :config="formConfig"
        :model="formData"
        @submit="handleFormSubmit"
        @reset="handleFormReset"
        @field-change="handleFieldChange"
      />
    </el-card>

    <!-- 操作按钮区域 -->
    <div class="form-actions" v-if="!isViewMode">
      <el-button @click="handleReset">
        <el-icon><Refresh /></el-icon>
        重置
      </el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        <el-icon><Check /></el-icon>
        {{ isEditMode ? '更新' : '保存' }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { useUserEdit } from './UserEdit.js'
import ConfigurableForm from '@/components/form/ConfigurableForm.vue'

// 使用用户编辑组合式函数
const {
  // 图标组件
  ArrowLeft,
  Refresh,
  Check,
  // 表单引用
  formRef,
  // 响应式数据
  isEditMode,
  isViewMode,
  submitting,
  pageTitle,
  pageDescription,
  formData,
  formConfig,
  // 方法
  handleBack,
  handleReset,
  handleSubmit,
  handleFormSubmit,
  handleFormReset,
  handleFieldChange
} = useUserEdit()
</script>

<style scoped src="./UserEdit.css"></style>
