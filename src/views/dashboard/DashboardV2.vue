<template>
  <div class="dashboard-v2-page">
    <!-- 顶部欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1 class="welcome-title">
            <el-icon class="welcome-icon"><Sunny /></el-icon>
            欢迎回来，{{ userInfo.realName || userInfo.username }}！
          </h1>
          <p class="welcome-desc">{{ currentDate }} · {{ currentTime }}</p>
          <p class="welcome-subtitle">今天也要加油哦～</p>
        </div>
        <div class="welcome-avatar">
          <el-avatar :size="80" :src="userInfo.avatar">
            {{ (userInfo.realName || userInfo.username)?.charAt(0) }}
          </el-avatar>
          <div class="online-status"></div>
        </div>
      </div>
    </div>

    <!-- 快速统计卡片 -->
    <div class="quick-stats">
      <div class="stat-card" v-for="stat in quickStats" :key="stat.key">
        <div class="stat-icon" :class="stat.iconClass">
          <el-icon :size="32">
            <component :is="stat.icon" />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-change" :class="stat.trend">
            <el-icon><component :is="stat.trendIcon" /></el-icon>
            {{ stat.change }}
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧内容 -->
      <div class="content-left">
        <!-- 数据概览图表 -->
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>数据概览</h3>
              <el-button-group size="small">
                <el-button 
                  v-for="period in chartPeriods" 
                  :key="period.value"
                  :type="activeChartPeriod === period.value ? 'primary' : ''"
                  @click="setChartPeriod(period.value)"
                >
                  {{ period.label }}
                </el-button>
              </el-button-group>
            </div>
          </template>
          <div class="chart-container">
            <div class="simple-line-chart">
              <div class="chart-grid">
                <div v-for="(point, index) in chartData" :key="index" class="chart-point">
                  <div class="point-bar" :style="{ height: point.value + '%' }">
                    <div class="point-dot"></div>
                  </div>
                  <div class="point-label">{{ point.label }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 最新活动 -->
        <el-card class="activity-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>最新活动</h3>
              <el-button link type="primary">查看全部</el-button>
            </div>
          </template>
          <div class="activity-timeline">
            <div v-for="activity in recentActivities" :key="activity.id" class="timeline-item">
              <div class="timeline-dot" :class="activity.type"></div>
              <div class="timeline-content">
                <div class="activity-header">
                  <span class="activity-title">{{ activity.title }}</span>
                  <span class="activity-time">{{ activity.time }}</span>
                </div>
                <div class="activity-desc">{{ activity.description }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧内容 -->
      <div class="content-right">
        <!-- 快速操作 -->
        <el-card class="quick-actions-card" shadow="hover">
          <template #header>
            <h3>快速操作</h3>
          </template>
          <div class="quick-actions">
            <div 
              v-for="action in quickActions" 
              :key="action.key"
              class="action-item"
              @click="handleQuickAction(action)"
            >
              <div class="action-icon" :class="action.iconClass">
                <el-icon :size="24">
                  <component :is="action.icon" />
                </el-icon>
              </div>
              <div class="action-text">
                <div class="action-title">{{ action.title }}</div>
                <div class="action-desc">{{ action.description }}</div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 系统状态 -->
        <el-card class="system-status-card" shadow="hover">
          <template #header>
            <h3>系统状态</h3>
          </template>
          <div class="system-metrics">
            <div v-for="metric in systemMetrics" :key="metric.key" class="metric-item">
              <div class="metric-header">
                <span class="metric-label">{{ metric.label }}</span>
                <span class="metric-value">{{ metric.value }}%</span>
              </div>
              <div class="metric-progress">
                <div class="progress-track">
                  <div 
                    class="progress-fill" 
                    :class="metric.status"
                    :style="{ width: metric.value + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 通知中心 -->
        <el-card class="notifications-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>通知中心</h3>
              <el-badge :value="unreadCount" class="notification-badge">
                <el-icon><Bell /></el-icon>
              </el-badge>
            </div>
          </template>
          <div class="notifications-list">
            <div v-for="notification in notifications" :key="notification.id" class="notification-item">
              <div class="notification-icon" :class="notification.type">
                <el-icon><component :is="notification.icon" /></el-icon>
              </div>
              <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-time">{{ notification.time }}</div>
              </div>
              <div class="notification-status" v-if="!notification.read"></div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useDashboardV2 } from './DashboardV2.js'

// 使用新版仪表板组合式函数
const {
  // 图标组件
  Sunny,
  User,
  ShoppingCart,
  Money,
  View,
  CaretTop,
  CaretBottom,
  Plus,
  Edit,
  Setting,
  Bell,
  Warning,
  SuccessFilled,
  // 响应式数据
  userInfo,
  currentDate,
  currentTime,
  quickStats,
  chartPeriods,
  activeChartPeriod,
  chartData,
  recentActivities,
  quickActions,
  systemMetrics,
  notifications,
  unreadCount,
  // 方法
  setChartPeriod,
  handleQuickAction
} = useDashboardV2()
</script>

<style scoped src="./DashboardV2.css"></style>
