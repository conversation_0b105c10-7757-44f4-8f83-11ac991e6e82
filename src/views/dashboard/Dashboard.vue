<template>
  <div class="dashboard-page">
    <!-- 欢迎信息 -->
    <div class="welcome-card">
      <div class="welcome-content">
        <h1 class="welcome-title">欢迎回来，{{ userInfo.realName || userInfo.username }}！</h1>
        <p class="welcome-desc">今天是 {{ currentDate }}，祝您工作愉快</p>
      </div>
      <div class="welcome-stats">
        <div class="stat-item">
          <div class="stat-number">{{ todayVisits }}</div>
          <div class="stat-label">今日访问</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ totalUsers }}</div>
          <div class="stat-label">总用户数</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ systemStatus }}</div>
          <div class="stat-label">系统状态</div>
        </div>
      </div>
    </div>

    <!-- 数据统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon users">
          <el-icon size="40"><User /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">1,234</div>
          <div class="stat-title">用户总数</div>
          <div class="stat-trend up">
            <el-icon><CaretTop /></el-icon>
            12.5%
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon orders">
          <el-icon size="40"><ShoppingCart /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">2,456</div>
          <div class="stat-title">订单数量</div>
          <div class="stat-trend up">
            <el-icon><CaretTop /></el-icon>
            8.7%
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon revenue">
          <el-icon size="40"><Money /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">¥89,567</div>
          <div class="stat-title">收入统计</div>
          <div class="stat-trend down">
            <el-icon><CaretBottom /></el-icon>
            3.2%
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon visits">
          <el-icon size="40"><View /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">45,678</div>
          <div class="stat-title">访问量</div>
          <div class="stat-trend up">
            <el-icon><CaretTop /></el-icon>
            15.3%
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid">
      <div class="chart-card">
        <div class="chart-header">
          <h3>访问量趋势</h3>
          <el-button-group size="small">
            <el-button :type="activeTab === 'day' ? 'primary' : ''" @click="setActiveTab('day')">日</el-button>
            <el-button :type="activeTab === 'week' ? 'primary' : ''" @click="setActiveTab('week')">周</el-button>
            <el-button :type="activeTab === 'month' ? 'primary' : ''" @click="setActiveTab('month')">月</el-button>
          </el-button-group>
        </div>
        <div class="chart-content">
          <div class="simple-chart">
            <div class="chart-bars">
              <div v-for="(item, index) in chartData" :key="index" class="chart-bar">
                <div class="bar" :style="{ height: item.value + '%' }"></div>
                <div class="bar-label">{{ item.label }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="chart-card">
        <div class="chart-header">
          <h3>用户分布</h3>
        </div>
        <div class="chart-content">
          <div class="pie-chart">
            <div class="pie-item" v-for="(item, index) in pieData" :key="index">
              <div class="pie-color" :style="{ backgroundColor: item.color }"></div>
              <div class="pie-label">{{ item.name }}</div>
              <div class="pie-value">{{ item.value }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最新动态 -->
    <div class="activity-grid">
      <div class="activity-card">
        <div class="activity-header">
          <h3>最新动态</h3>
          <el-button link>查看全部</el-button>
        </div>
        <div class="activity-list">
          <div v-for="activity in activities" :key="activity.id" class="activity-item">
            <div class="activity-avatar">
              <el-avatar :size="32" :src="activity.avatar">
                {{ activity.name.charAt(0) }}
              </el-avatar>
            </div>
            <div class="activity-content">
              <div class="activity-text">
                <strong>{{ activity.name }}</strong> {{ activity.action }}
              </div>
              <div class="activity-time">{{ activity.time }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="activity-card">
        <div class="activity-header">
          <h3>系统监控</h3>
        </div>
        <div class="monitor-list">
          <div class="monitor-item">
            <div class="monitor-label">CPU使用率</div>
            <div class="monitor-progress">
              <div class="progress-bar">
                <div class="progress-fill" style="width: 65%"></div>
              </div>
              <div class="progress-text">65%</div>
            </div>
          </div>
          <div class="monitor-item">
            <div class="monitor-label">内存使用率</div>
            <div class="monitor-progress">
              <div class="progress-bar">
                <div class="progress-fill" style="width: 82%"></div>
              </div>
              <div class="progress-text">82%</div>
            </div>
          </div>
          <div class="monitor-item">
            <div class="monitor-label">磁盘使用率</div>
            <div class="monitor-progress">
              <div class="progress-bar">
                <div class="progress-fill" style="width: 45%"></div>
              </div>
              <div class="progress-text">45%</div>
            </div>
          </div>
          <div class="monitor-item">
            <div class="monitor-label">网络带宽</div>
            <div class="monitor-progress">
              <div class="progress-bar">
                <div class="progress-fill" style="width: 28%"></div>
              </div>
              <div class="progress-text">28%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useDashboard } from './Dashboard.js'

// 使用仪表板组合式函数
const {
  // 图标组件
  User,
  ShoppingCart,
  Money,
  View,
  CaretTop,
  CaretBottom,
  // 响应式数据
  userInfo,
  currentDate,
  todayVisits,
  totalUsers,
  systemStatus,
  activeTab,
  chartData,
  pieData,
  activities,
  // 方法
  setActiveTab
} = useDashboard()
</script>

<style scoped src="./Dashboard.css"></style>
