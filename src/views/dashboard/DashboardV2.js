import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Sunny,
  User,
  ShoppingCart,
  Money,
  View,
  CaretTop,
  CaretBottom,
  Plus,
  Edit,
  Setting,
  Bell,
  Warning,
  SuccessFilled,
  InfoFilled
} from '@element-plus/icons-vue'
import { getUserInfo } from '@/router/index.js'

/**
 * 新版控制台页面组合式函数
 * @returns {Object} 返回模板需要的所有数据和方法
 */
export function useDashboardV2() {
  const router = useRouter()

  // 用户信息
  const userInfo = ref({
    username: 'admin',
    realName: '管理员',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
  })

  // 当前时间
  const currentDate = ref('')
  const currentTime = ref('')
  let timeInterval = null

  // 图表周期选择
  const activeChartPeriod = ref('week')
  const chartPeriods = [
    { label: '今日', value: 'day' },
    { label: '本周', value: 'week' },
    { label: '本月', value: 'month' }
  ]

  // 快速统计数据
  const quickStats = ref([
    {
      key: 'users',
      label: '总用户数',
      value: '2,847',
      change: '+12.5%',
      trend: 'up',
      icon: User,
      trendIcon: CaretTop,
      iconClass: 'users'
    },
    {
      key: 'orders',
      label: '订单数量',
      value: '1,234',
      change: '+8.7%',
      trend: 'up',
      icon: ShoppingCart,
      trendIcon: CaretTop,
      iconClass: 'orders'
    },
    {
      key: 'revenue',
      label: '收入统计',
      value: '¥89,567',
      change: '-3.2%',
      trend: 'down',
      icon: Money,
      trendIcon: CaretBottom,
      iconClass: 'revenue'
    },
    {
      key: 'visits',
      label: '访问量',
      value: '45,678',
      change: '+15.3%',
      trend: 'up',
      icon: View,
      trendIcon: CaretTop,
      iconClass: 'visits'
    }
  ])

  // 图表数据
  const chartData = ref([
    { label: '周一', value: 65 },
    { label: '周二', value: 78 },
    { label: '周三', value: 82 },
    { label: '周四', value: 71 },
    { label: '周五', value: 95 },
    { label: '周六', value: 88 },
    { label: '周日', value: 76 }
  ])

  // 最新活动
  const recentActivities = ref([
    {
      id: 1,
      title: '新用户注册',
      description: '用户"张三"成功注册账号',
      time: '2分钟前',
      type: 'success'
    },
    {
      id: 2,
      title: '订单创建',
      description: '订单#12345已创建，等待支付',
      time: '5分钟前',
      type: 'info'
    },
    {
      id: 3,
      title: '系统更新',
      description: '系统版本更新至v2.1.0',
      time: '1小时前',
      type: 'warning'
    },
    {
      id: 4,
      title: '数据备份',
      description: '每日数据备份已完成',
      time: '2小时前',
      type: 'success'
    }
  ])

  // 快速操作
  const quickActions = ref([
    {
      key: 'add-user',
      title: '新增用户',
      description: '快速添加新用户',
      icon: Plus,
      iconClass: 'add',
      route: '/admin/user-list-v2/user-create-v2'
    },
    {
      key: 'add-product',
      title: '新增商品',
      description: '添加新的商品',
      icon: Plus,
      iconClass: 'product',
      route: '/admin/product-list/product-create'
    },
    {
      key: 'system-config',
      title: '系统配置',
      description: '管理系统设置',
      icon: Setting,
      iconClass: 'config',
      route: '/admin/config-list'
    },
    {
      key: 'user-manage',
      title: '用户管理',
      description: '管理用户信息',
      icon: Edit,
      iconClass: 'manage',
      route: '/admin/user-list-v2'
    }
  ])

  // 系统指标
  const systemMetrics = ref([
    {
      key: 'cpu',
      label: 'CPU使用率',
      value: 65,
      status: 'normal'
    },
    {
      key: 'memory',
      label: '内存使用率',
      value: 82,
      status: 'warning'
    },
    {
      key: 'disk',
      label: '磁盘使用率',
      value: 45,
      status: 'normal'
    },
    {
      key: 'network',
      label: '网络带宽',
      value: 28,
      status: 'good'
    }
  ])

  // 通知数据
  const notifications = ref([
    {
      id: 1,
      title: '系统维护通知',
      time: '10分钟前',
      type: 'warning',
      icon: Warning,
      read: false
    },
    {
      id: 2,
      title: '新功能上线',
      time: '1小时前',
      type: 'success',
      icon: SuccessFilled,
      read: false
    },
    {
      id: 3,
      title: '数据报告已生成',
      time: '2小时前',
      type: 'info',
      icon: InfoFilled,
      read: true
    }
  ])

  // 未读通知数量
  const unreadCount = computed(() => {
    return notifications.value.filter(n => !n.read).length
  })

  // 更新时间
  const updateTime = () => {
    const now = new Date()
    currentDate.value = now.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })
    currentTime.value = now.toLocaleTimeString('zh-CN', {
      hour12: false
    })
  }

  // 设置图表周期
  const setChartPeriod = (period) => {
    activeChartPeriod.value = period
    
    // 根据周期更新图表数据
    const chartDataMap = {
      day: [
        { label: '00:00', value: 45 },
        { label: '04:00', value: 32 },
        { label: '08:00', value: 68 },
        { label: '12:00', value: 85 },
        { label: '16:00', value: 92 },
        { label: '20:00', value: 76 },
        { label: '24:00', value: 58 }
      ],
      week: [
        { label: '周一', value: 65 },
        { label: '周二', value: 78 },
        { label: '周三', value: 82 },
        { label: '周四', value: 71 },
        { label: '周五', value: 95 },
        { label: '周六', value: 88 },
        { label: '周日', value: 76 }
      ],
      month: [
        { label: '第1周', value: 72 },
        { label: '第2周', value: 85 },
        { label: '第3周', value: 78 },
        { label: '第4周', value: 91 }
      ]
    }
    
    chartData.value = chartDataMap[period] || chartDataMap.week
    ElMessage.success(`已切换到${chartPeriods.find(p => p.value === period)?.label}视图`)
  }

  // 处理快速操作
  const handleQuickAction = (action) => {
    if (action.route) {
      router.push(action.route)
      ElMessage.success(`正在跳转到${action.title}`)
    } else {
      ElMessage.info(`${action.title}功能开发中...`)
    }
  }

  // 初始化用户信息
  const initUserInfo = () => {
    const user = getUserInfo()
    if (user) {
      userInfo.value = { ...userInfo.value, ...user }
    }
  }

  // 页面挂载时初始化
  onMounted(() => {
    initUserInfo()
    updateTime()
    timeInterval = setInterval(updateTime, 1000)
  })

  // 页面卸载时清理
  onUnmounted(() => {
    if (timeInterval) {
      clearInterval(timeInterval)
    }
  })

  // 返回模板需要的所有数据和方法
  return {
    // 图标组件
    Sunny,
    User,
    ShoppingCart,
    Money,
    View,
    CaretTop,
    CaretBottom,
    Plus,
    Edit,
    Setting,
    Bell,
    Warning,
    SuccessFilled,
    // 响应式数据
    userInfo,
    currentDate,
    currentTime,
    quickStats,
    chartPeriods,
    activeChartPeriod,
    chartData,
    recentActivities,
    quickActions,
    systemMetrics,
    notifications,
    unreadCount,
    // 方法
    setChartPeriod,
    handleQuickAction
  }
}
