# Views 目录结构说明

本目录已按功能模块重新组织，提高代码的可维护性和可读性。

## 📁 目录结构

```
src/views/
├── auth/           # 认证相关页面
│   ├── Login.vue   # 登录页面
│   ├── Login.js    # 登录逻辑
│   └── Login.css   # 登录样式
├── dashboard/      # 仪表板页面
│   ├── Dashboard.vue
│   ├── Dashboard.js
│   └── Dashboard.css
├── user/           # 用户管理模块
│   ├── UserList.vue        # 用户列表（原版）
│   ├── UserList.js
│   ├── UserList.css
│   ├── user_list_v2.vue    # 用户列表（简化版）
│   ├── user_list_v2.js
│   ├── user_list_v2.css
│   ├── user_create_v2.vue  # 用户创建（简化版）
│   ├── user_create_v2.js
│   ├── user_create_v2.css
│   ├── UserEdit.vue        # 用户编辑
│   ├── UserEdit.js
│   └── UserEdit.css
├── demo/           # 演示页面
│   ├── ComponentDemo.vue   # 组件演示
│   ├── ComponentDemo.js
│   ├── SimpleDemo.vue      # 超简化组件演示
│   ├── Form.vue           # 表单演示
│   └── Table.vue          # 表格演示
├── system/         # 系统管理页面
│   ├── PermissionTest.vue  # 权限测试
│   ├── PermissionTest.js
│   └── PermissionTest.css
└── common/         # 通用页面
    ├── Layout.vue  # 布局组件
    └── NotFound.vue # 404页面
```

## 🎯 分类说明

### 🔐 auth/ - 认证模块
- **Login**: 用户登录页面，包含登录表单和验证逻辑

### 📊 dashboard/ - 仪表板模块  
- **Dashboard**: 管理后台首页，显示系统概览和统计信息

### 👥 user/ - 用户管理模块
- **UserList**: 传统用户列表页面，功能完整
- **user_list_v2**: 简化配置版用户列表，使用 SimpleTable 组件
- **user_create_v2**: 简化配置版用户创建，使用 SimpleForm 组件
- **UserEdit**: 用户编辑页面

### 🧪 demo/ - 演示模块
- **ComponentDemo**: 传统组件演示页面
- **SimpleDemo**: 超简化组件使用演示，展示最新功能
- **Form**: 表单组件演示
- **Table**: 表格组件演示

### ⚙️ system/ - 系统模块
- **PermissionTest**: 权限系统测试页面

### 🔧 common/ - 通用模块
- **Layout**: 主布局组件，包含侧边栏、顶栏等
- **NotFound**: 404错误页面

## 🚀 最新功能亮点

### user_create_v2.vue - 用户创建页面
- ✅ **统一事件处理**: 使用 `onEvent` 统一处理所有上传事件
- ✅ **双模式上传**: 支持服务器上传和本地预览两种模式
- ✅ **智能限制**: 自动处理文件数量限制，无需手动控制
- ✅ **图片预览**: 高级图片预览弹窗，支持缩放旋转
- ✅ **配置集中**: 初始数据在 formConfig 中统一管理

### SimpleDemo.vue - 功能演示页面
- 📖 **完整示例**: 提供详细的使用方法和代码示例
- 🎯 **功能展示**: 演示所有最新功能特性
- 📱 **响应式**: 完美适配移动端和桌面端

## 📝 使用建议

1. **新功能开发**: 优先使用 `demo/` 目录下的简化组件
2. **用户管理**: 推荐使用 `user/user_*_v2.vue` 系列页面
3. **学习参考**: 查看 `demo/SimpleDemo.vue` 了解最佳实践
4. **功能扩展**: 基于现有模块结构添加新功能

## 🔄 迁移说明

路由配置已自动更新，无需手动修改。如果有自定义引用，请更新为新路径：

```javascript
// 旧路径
import Login from '@/views/Login.vue'

// 新路径  
import Login from '@/views/auth/Login.vue'
```

## 📞 技术支持

如有问题，请参考各模块的代码注释或查看 `demo/SimpleDemo.vue` 中的使用示例。
