import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { authAPI } from '@/api'
import { ROLE_CONFIG, ROLE_PERMISSIONS } from '@/config/permissions'
import testAccountsConfig from '@/config/test-accounts.json'

/**
 * 登录页面组合式函数
 * @returns {Object} 返回模板需要的所有数据和方法
 */
export function useLogin() {
  const router = useRouter()
  const route = useRoute()

  // 表单引用
  const loginFormRef = ref()

  // 加载状态
  const loading = ref(false)

  // 登录表单数据
  const loginForm = reactive({
    username: '',
    password: '',
    role: 'admin',
    remember: false
  })

  // 表单验证规则
  const loginRules = reactive({
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 4, max: 20, message: '密码长度在 4 到 20 个字符', trigger: 'blur' }
    ],
    role: [
      { required: true, message: '请选择角色', trigger: 'change' }
    ]
  })

  // 测试账号配置 - 从JSON文件导入
  const testAccounts = ref(testAccountsConfig.testAccounts.map(account => ({
    ...account,
    desc: account.features ? account.features.join(' | ') : account.description.substring(0, 20) + '...'
  })))

  // 快速登录选项
  const quickLoginOptions = ref(testAccountsConfig.quickLoginOptions)

  // 角色选项 - 从配置生成
  const roleOptions = computed(() => {
    return testAccounts.value.map(account => ({
      value: account.role,
      label: account.label,
      desc: account.desc,
      color: account.color
    }))
  })

  // 角色改变时自动填充账号密码
  const handleRoleChange = (selectedRole) => {
    const account = testAccounts.value.find(acc => acc.role === selectedRole)
    if (account) {
      loginForm.username = account.username
      loginForm.password = account.password
    }
  }

  // 快速登录
  const quickLogin = (account) => {
    loginForm.role = account.role
    loginForm.username = account.username
    loginForm.password = account.password
    
    // 自动触发登录
    nextTick(() => {
      handleLogin()
    })
  }

  // 登录处理
  const handleLogin = async () => {
    if (!loginFormRef.value) return
    
    try {
      // 验证表单
      await loginFormRef.value.validate()
      
      loading.value = true
      
      try {
        // 使用新的API结构进行登录
        const response = await authAPI.login({
          username: loginForm.username,
          password: loginForm.password,
          role: loginForm.role
        })
        
        // 保存登录信息
        if (response.data) {

          console.log('登录成功:', response.data)

          response.data.role = "admin"

          localStorage.setItem('token', response.data.token || '')
          localStorage.setItem('userInfo', JSON.stringify(response.data))
          
          // 记住密码功能
          // if (loginForm.remember) {
          //   localStorage.setItem('rememberedPassword', loginForm.password)
          // } else {
          //   localStorage.removeItem('rememberedPassword')
          // }
          
          ElMessage.success('登录成功！')
          
          
          // 跳转到目标页面或首页
          const redirect = route.query.redirect || '/admin'
          router.push(redirect)
        }
      } catch (apiError) {
        // API调用失败，使用Mock数据作为后备
        // console.warn('API登录失败，使用Mock数据:', apiError)
        
        // // 模拟登录逻辑 - 支持所有角色
        // const selectedAccount = testAccounts.value.find(acc => acc.role === loginForm.role)
        
        // if (selectedAccount && 
        //     loginForm.username === selectedAccount.username && 
        //     loginForm.password === selectedAccount.password) {
        //   // 模拟用户信息和token
        //   const userInfo = {
        //     id: 1,
        //     username: selectedAccount.username,
        //     realName: selectedAccount.label,
        //     role: selectedAccount.role,
        //     avatar: ''
        //   }
          
        //   const token = 'mock-jwt-token-' + Date.now()
          
        //   // 保存用户信息到本地存储
        //   localStorage.setItem('token', token)
        //   localStorage.setItem('userInfo', JSON.stringify(userInfo))
          
        //   // 记住密码功能
        //   if (loginForm.remember) {
        //     localStorage.setItem('rememberedPassword', loginForm.password)
        //   } else {
        //     localStorage.removeItem('rememberedPassword')
        //   }
          
        //   ElMessage.success('登录成功！')
          
        //   // 跳转到目标页面或首页
        //   const redirect = route.query.redirect || '/admin'
        //   router.push(redirect)
        // } else {
          //ElMessage.error('用户名或密码错误')
        // }
      }
    } catch (error) {
      console.error('登录失败:', error)
      ElMessage.error('登录失败，请重试')
    } finally {
      loading.value = false
    }
  }

  // 页面初始化
  onMounted(() => {
    // 恢复记住的密码
    const rememberedPassword = localStorage.getItem('rememberedPassword')
    if (rememberedPassword) {
      loginForm.password = rememberedPassword
      loginForm.remember = true
    }
    
    // 自动填充测试账号（开发环境）
    // if (import.meta.env.DEV) {
    //   loginForm.username = 'admin'
    //   if (!loginForm.password) {
    //     loginForm.password = '123456'
    //   }
    // }
  })

  // 返回模板需要的所有数据和方法
  return {
    // 图标组件
    User,
    Lock,
    // 表单引用
    loginFormRef,
    // 响应式数据
    loading,
    loginForm,
    loginRules,
    testAccounts,
    quickLoginOptions,
    roleOptions,
    // 方法
    handleRoleChange,
    quickLogin,
    handleLogin
  }
}
