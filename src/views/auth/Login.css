.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.login-box {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #333;
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 10px 0;
}

.login-header p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.login-form {
  margin-top: 20px;
}

.login-button {
  width: 100%;
  height: 45px;
  font-size: 16px;
  border-radius: 6px;
}

.login-footer {
  text-align: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.login-footer p {
  color: #999;
  font-size: 12px;
  margin: 0;
}

.test-accounts {
  margin-top: 20px;
}

.account-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.account-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  background: #f5f5f5;
  cursor: pointer;
  transition: background-color 0.2s;
}

.account-item:hover {
  background: #e6f7ff;
}

/* 响应式设计 */
@media (min-width: 1024px) {
  .login-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
  }
  
  .login-box {
    width: 450px;
    padding: 50px;
    margin: 20px;
    max-width: none;
  }
  
  .login-header h2 {
    font-size: 32px;
  }
  
  .login-header p {
    font-size: 16px;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .login-box {
    width: 420px;
    padding: 40px;
  }
  
  .login-header h2 {
    font-size: 30px;
  }
}

@media (min-width: 481px) and (max-width: 767px) {
  .login-box {
    width: 380px;
    padding: 35px;
  }
  
  .login-header h2 {
    font-size: 26px;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 20px;
  }
  
  .login-box {
    width: 100%;
    max-width: 350px;
    padding: 30px 20px;
    margin: 0;
  }
  
  .login-header h2 {
    font-size: 24px;
  }
  
  .login-header p {
    font-size: 13px;
  }
}

/* 超大屏幕优化 */
@media (min-width: 1440px) {
  .login-box {
    width: 500px;
    padding: 60px;
  }
  
  .login-header h2 {
    font-size: 36px;
  }
  
  .login-header p {
    font-size: 18px;
  }
  
  .login-button {
    height: 50px;
    font-size: 18px;
  }
}
