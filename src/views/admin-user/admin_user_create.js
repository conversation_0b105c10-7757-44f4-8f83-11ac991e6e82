import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Refresh, Check } from '@element-plus/icons-vue'
import { adminUserAPI } from '@/api'

/**
 * 后台用户创建页面组合式函数
 * @returns {Object} 返回模板需要的所有数据和方法
 */
export function useAdminUserCreate() {
  const router = useRouter()
  const route = useRoute()

  // 表单引用
  const formRef = ref()

  // 页面状态
  const submitting = ref(false)
  const pageTitle = ref('新增管理员')

  // 表单数据
  const formData = reactive({})

  // 表单配置
  const formConfig = reactive({
    labelWidth: '120px',
    // 初始数据配置
    initialData: {
      username: '',
      password: '',
      confirmPassword: '',
      role: 'editor',
      email: '',
      phone: '',
      remark: ''
    },
    fields: [
      {
        type: 'input',
        prop: 'username',
        label: '用户名',
        placeholder: '请输入用户名',
        rules: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '用户名只能包含字母、数字和下划线，且以字母开头', trigger: 'blur' }
        ]
      },
      {
        type: 'password',
        prop: 'password',
        label: '密码',
        placeholder: '请输入密码',
        rules: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
        ]
      },
      {
        type: 'password',
        prop: 'confirmPassword',
        label: '确认密码',
        placeholder: '请再次输入密码',
        rules: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value !== formData.password) {
                callback(new Error('两次输入密码不一致'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },
      {
        type: 'select',
        prop: 'role',
        label: '角色',
        placeholder: '请选择角色',
        options: [
          { label: '超级管理员', value: 'superadmin' },
          { label: '管理员', value: 'admin' },
          { label: '编辑员', value: 'editor' },
          { label: '普通用户', value: 'user' }
        ],
        rules: [
          { required: true, message: '请选择角色', trigger: 'change' }
        ]
      },
      {
        type: 'input',
        prop: 'email',
        label: '邮箱',
        placeholder: '请输入邮箱地址',
        rules: [
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ]
      },
      {
        type: 'input',
        prop: 'phone',
        label: '手机号',
        placeholder: '请输入手机号',
        rules: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
        ]
      },
      {
        type: 'textarea',
        prop: 'remark',
        label: '备注',
        placeholder: '请输入备注信息',
        rows: 3,
        rules: [
          { max: 200, message: '备注不能超过200个字符', trigger: 'blur' }
        ]
      }
    ],
    actions: [
      { type: 'submit', label: '保存', style: 'primary' },
      { type: 'reset', label: '重置', style: 'default' }
    ]
  })

  // 初始化表单数据
  Object.assign(formData, formConfig.initialData)

  // 返回上一页
  const handleBack = () => {
    router.back()
  }

  // 重置表单
  const handleReset = () => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
    // 从配置中重置数据
    Object.assign(formData, formConfig.initialData)
    ElMessage.info('表单已重置')
  }

  // 提交表单
  const handleSubmit = async () => {
    try {
      // 表单验证
      await formRef.value?.validate()

      submitting.value = true

      // 准备提交数据
      const adminUserData = { ...formData }
      delete adminUserData.confirmPassword // 删除确认密码字段

      console.log('创建管理员数据:', adminUserData)

      // 调用API创建管理员
      await adminUserAPI.createAdminUser(adminUserData)

      ElMessage.success('管理员创建成功！')

      // 跳转回管理员列表
      setTimeout(() => {
        router.push('/admin/admin-user-list')
      }, 1000)

    } catch (error) {
      if (error !== 'cancel') {
        console.error('创建管理员失败:', error)
        ElMessage.error('创建管理员失败，请检查表单信息')
      }
    } finally {
      submitting.value = false
    }
  }

  // 表单提交处理（由SimpleForm触发）
  const handleFormSubmit = (data) => {
    Object.assign(formData, data)
    handleSubmit()
  }

  // 表单重置处理（由SimpleForm触发）
  const handleFormReset = () => {
    handleReset()
  }

  // 字段变化处理
  const handleFieldChange = (prop, value) => {
    console.log(`[AdminUserCreate] Field change: ${prop}`, value)

    // 更新表单数据
    if (typeof prop === 'object') {
      // 如果传入的是对象，批量更新
      Object.assign(formData, prop)
    } else {
      // 单个字段更新
      formData[prop] = value
    }
  }

  // 返回模板需要的所有数据和方法
  return {
    // 图标组件
    ArrowLeft,
    Refresh,
    Check,
    // 表单引用
    formRef,
    // 响应式数据
    submitting,
    pageTitle,
    formData,
    formConfig,
    // 方法
    handleBack,
    handleReset,
    handleSubmit,
    handleFormSubmit,
    handleFormReset,
    handleFieldChange
  }
}
