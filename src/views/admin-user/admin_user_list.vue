<template>
  <div class="admin-user-list-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">后台用户管理</h1>
      <div class="header-actions">
        <el-button 
          v-permission="'admin_user:create'"
          type="primary" 
          @click="handleAdd"
        >
          <el-icon><Plus /></el-icon>
          新增管理员
        </el-button>
      </div>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchFormData" inline>
        <el-form-item label="用户名">
          <el-input 
            v-model="searchFormData.username" 
            placeholder="请输入用户名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="登录状态">
          <el-select 
            v-model="searchFormData.login_status" 
            placeholder="请选择登录状态"
            clearable
            style="width: 150px"
          >
            <el-option label="在线" value="1" />
            <el-option label="离线" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <SimpleTable
        :data="tableData"
        :config="tableConfig"
        :loading="loading"
        :total="total"
        :current-page="currentPage"
        :page-size="pageSize"
        @action="handleTableAction"
        @selection-change="handleSelectionChange"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
      >
        <!-- 登录状态插槽 -->
        <template #login_status="{ row, value }">
          <el-switch
            :model-value="value"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
            @change="(newValue) => handleLoginStatusChange(newValue, row)"
            :disabled="loading"
          />
        </template>
      </SimpleTable>
    </el-card>

    <!-- 子路由视图 -->
    <router-view />
  </div>
</template>

<script setup>
import { useAdminUserList } from './admin_user_list.js'
import SimpleTable from '@/components/table/SimpleTable.vue'

// 使用后台用户列表组合式函数
const {
  // 图标组件
  Plus,
  Search,
  Refresh,
  // 响应式数据
  searchFormData,
  tableData,
  tableConfig,
  loading,
  total,
  currentPage,
  pageSize,
  // 方法
  handleAdd,
  handleSearch,
  handleReset,
  handleTableAction,
  handleLoginStatusChange,
  handleSelectionChange,
  handlePageChange,
  handleSizeChange
} = useAdminUserList()
</script>

<style scoped src="./admin_user_list.css"></style>
