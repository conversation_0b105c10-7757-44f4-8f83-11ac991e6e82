# 订单管理模块

## 概述

订单管理模块是基于 Swagger API 文档中的后台订单管理接口开发的，提供了完整的订单管理功能，包括订单列表查看、订单创建等功能。

## 功能特性

### 1. 订单列表 (order_list.vue)
- **分页展示**: 支持分页查看订单数据
- **多条件搜索**: 支持按订单号、用户昵称、商品名称、订单状态进行搜索
- **状态管理**: 订单状态包括待处理、已完成、已取消
- **操作功能**: 支持查看、编辑、删除订单
- **批量选择**: 支持批量选择订单进行操作

### 2. 订单创建 (order_create.vue)
- **表单验证**: 完整的表单验证规则
- **自动填充**: 选择商品后自动填充商品名称
- **时间选择**: 支持选择使用时间和时间段
- **状态设置**: 可设置订单初始状态

## 文件结构

```
src/views/order/
├── order_list.vue          # 订单列表页面
├── order_list.js           # 订单列表逻辑
├── order_list.css          # 订单列表样式
├── order_create.vue        # 订单创建页面
├── order_create.js         # 订单创建逻辑
├── order_create.css        # 订单创建样式
└── README.md               # 模块说明文档
```

## API 接口

基于 Swagger 文档中的以下接口：

### 订单列表接口
- **路径**: `{{host}}/admin/order/list`
- **方法**: GET
- **参数**: 
  - `page`: 页码
  - `limit`: 每页数量
- **响应示例**:
```json
{
  "code": 200,
  "msg": "成功",
  "data": {
    "list": [
      {
        "attach": null,
        "create_time": "2025-05-06 15:00:52",
        "id": 1,
        "nickname": "昵称",
        "product_id": 20,
        "product_name": "高级瑜伽课程",
        "product_time_id": 16,
        "remark": "谁谁谁",
        "status": 0,
        "trade_no": 20250506150051410,
        "unionid": "123456",
        "update_time": "2025-05-06 15:00:52",
        "use_time": "2025-04-18T18:26:39+08:00",
        "user_id": 1
      }
    ],
    "total": 1
  }
}
```

## 数据字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Number | 订单ID |
| trade_no | String | 订单号 |
| nickname | String | 用户昵称 |
| product_id | Number | 商品ID |
| product_name | String | 商品名称 |
| product_time_id | Number | 时间段ID |
| status | Number | 订单状态 (0:待处理, 1:已完成, 2:已取消) |
| use_time | String | 使用时间 |
| remark | String | 备注 |
| unionid | String | 用户唯一标识 |
| user_id | Number | 用户ID |
| create_time | String | 创建时间 |
| update_time | String | 更新时间 |
| attach | String | 附件 |

## 权限配置

模块已配置以下权限：
- `order:view` - 查看订单
- `order:create` - 创建订单
- `order:edit` - 编辑订单
- `order:delete` - 删除订单
- `order:export` - 导出订单

## 路由配置

已在路由中添加以下路由：
- `/admin/order-list` - 订单列表页面
- `/admin/order-create` - 订单创建页面

## 菜单配置

已在左侧菜单中添加"订单管理"菜单项，包含订单列表子菜单。

## 使用说明

1. **访问订单列表**: 登录后台管理系统，在左侧菜单点击"订单管理" -> "订单列表"
2. **搜索订单**: 在搜索区域输入搜索条件，点击"搜索"按钮
3. **创建订单**: 在订单列表页面点击"新增订单"按钮
4. **编辑订单**: 在订单列表中点击"编辑"按钮
5. **删除订单**: 在订单列表中点击"删除"按钮，确认后删除

## 技术特点

- **组合式API**: 使用 Vue 3 Composition API 开发
- **简化配置**: 参考用户管理模块的简化配置方式
- **响应式设计**: 支持移动端和桌面端
- **权限控制**: 集成权限管理系统
- **表单验证**: 完整的表单验证机制
- **状态管理**: 统一的状态管理和错误处理

## 扩展功能

可以根据需要扩展以下功能：
- 订单详情页面
- 订单状态批量更新
- 订单导出功能
- 订单统计报表
- 订单支付状态管理
