<template>
  <div class="order-list-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">订单列表</h1>
      <div class="header-actions">
        <el-button 
          v-permission="'order:create'"
          type="primary" 
          @click="handleAdd"
        >
          <el-icon><Plus /></el-icon>
          新增订单
        </el-button>
      </div>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchFormData" inline>
        <el-form-item label="订单号">
          <el-input 
            v-model="searchFormData.trade_no" 
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="用户昵称">
          <el-input 
            v-model="searchFormData.nickname" 
            placeholder="请输入用户昵称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="商品名称">
          <el-input 
            v-model="searchFormData.product_name" 
            placeholder="请输入商品名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select 
            v-model="searchFormData.status" 
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="待处理" value="0" />
            <el-option label="已完成" value="1" />
            <el-option label="已取消" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <SimpleTable 
        :data="tableData"
        :config="tableConfig"
        :loading="loading"
        :total="total"
        :current-page="currentPage"
        :page-size="pageSize"
        @action="handleTableAction"
        @selection-change="handleSelectionChange"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </el-card>
  </div>
</template>

<script setup>
import { useOrderList } from './order_list.js'
import SimpleTable from '@/components/table/SimpleTable.vue'

// 使用订单列表组合式函数
const {
  // 图标组件
  Plus,
  Search,
  Refresh,
  // 响应式数据
  searchFormData,
  tableData,
  tableConfig,
  loading,
  total,
  currentPage,
  pageSize,
  // 方法
  handleAdd,
  handleSearch,
  handleReset,
  handleTableAction,
  handleSelectionChange,
  handlePageChange,
  handleSizeChange
} = useOrderList()
</script>

<style scoped src="./order_list.css"></style>
