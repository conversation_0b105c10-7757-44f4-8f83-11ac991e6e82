import { ref, reactive, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Refresh, Check } from '@element-plus/icons-vue'
import { orderAPI } from '@/api'

/**
 * 订单创建页面组合式函数
 * @returns {Object} 返回模板需要的所有数据和方法
 */
export function useOrderCreate() {
  const router = useRouter()
  const route = useRoute()

  // 表单引用
  const formRef = ref()

  // 页面状态
  const submitting = ref(false)
  const pageTitle = ref('新增订单')

  // 表单数据
  const formData = reactive({})

  // 表单配置
  const formConfig = reactive({
    labelWidth: '120px',
    // 初始数据配置
    initialData: {
      trade_no: '',
      nickname: '',
      product_id: '',
      product_name: '',
      product_time_id: '',
      status: '0',
      use_time: '',
      remark: '',
      unionid: '',
      user_id: ''
    },
    fields: [
      {
        type: 'input',
        prop: 'trade_no',
        label: '订单号',
        placeholder: '请输入订单号',
        rules: [
          { required: true, message: '请输入订单号', trigger: 'blur' }
        ]
      },
      {
        type: 'input',
        prop: 'nickname',
        label: '用户昵称',
        placeholder: '请输入用户昵称',
        rules: [
          { required: true, message: '请输入用户昵称', trigger: 'blur' }
        ]
      },
      {
        type: 'input',
        prop: 'unionid',
        label: '用户ID',
        placeholder: '请输入用户ID',
        rules: [
          { required: true, message: '请输入用户ID', trigger: 'blur' }
        ]
      },
      {
        type: 'select',
        prop: 'product_id',
        label: '商品',
        placeholder: '请选择商品',
        options: [
          { label: '高级瑜伽课程', value: '20' },
          { label: '初级瑜伽课程', value: '21' },
          { label: '冥想课程', value: '22' },
          { label: '普拉提课程', value: '23' },
          { label: '舞蹈课程', value: '24' }
        ],
        rules: [
          { required: true, message: '请选择商品', trigger: 'change' }
        ]
      },
      {
        type: 'input',
        prop: 'product_name',
        label: '商品名称',
        placeholder: '商品名称（自动填充）',
        disabled: true
      },
      {
        type: 'select',
        prop: 'product_time_id',
        label: '时间段',
        placeholder: '请选择时间段',
        options: [
          { label: '上午 09:00-11:00', value: '16' },
          { label: '下午 14:00-16:00', value: '17' },
          { label: '晚上 18:00-20:00', value: '18' },
          { label: '上午 10:00-12:00', value: '19' },
          { label: '晚上 19:00-21:00', value: '20' }
        ],
        rules: [
          { required: true, message: '请选择时间段', trigger: 'change' }
        ]
      },
      {
        type: 'datetime',
        prop: 'use_time',
        label: '使用时间',
        placeholder: '请选择使用时间',
        rules: [
          { required: true, message: '请选择使用时间', trigger: 'change' }
        ]
      },
      {
        type: 'radio',
        prop: 'status',
        label: '订单状态',
        options: [
          { label: '待处理', value: '0' },
          { label: '已完成', value: '1' },
          { label: '已取消', value: '2' }
        ],
        rules: [
          { required: true, message: '请选择订单状态', trigger: 'change' }
        ]
      },
      {
        type: 'textarea',
        prop: 'remark',
        label: '备注',
        placeholder: '请输入备注信息',
        rows: 3
      }
    ],
    actions: [
      { type: 'submit', label: '保存', style: 'primary' },
      { type: 'reset', label: '重置', style: 'default' }
    ]
  })

  // 初始化表单数据
  Object.assign(formData, formConfig.initialData)

  // 商品名称映射
  const productNameMap = {
    '20': '高级瑜伽课程',
    '21': '初级瑜伽课程',
    '22': '冥想课程',
    '23': '普拉提课程',
    '24': '舞蹈课程'
  }

  // 返回上一页
  const handleBack = () => {
    router.back()
  }

  // 重置表单
  const handleReset = () => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
    // 从配置中重置数据
    Object.assign(formData, formConfig.initialData)
    ElMessage.info('表单已重置')
  }

  // 提交表单
  const handleSubmit = async () => {
    try {
      // 表单验证
      await formRef.value?.validate()

      submitting.value = true

      // 准备提交数据
      const orderData = { ...formData }

      console.log('创建订单数据:', orderData)

      // 调用API创建订单
      await orderAPI.createOrder(orderData)

      ElMessage.success('订单创建成功！')

      // 跳转回订单列表
      setTimeout(() => {
        router.push('/admin/order-list')
      }, 1000)

    } catch (error) {
      if (error !== 'cancel') {
        console.error('创建订单失败:', error)
        ElMessage.error('创建订单失败，请检查表单信息')
      }
    } finally {
      submitting.value = false
    }
  }

  // 表单提交处理（由SimpleForm触发）
  const handleFormSubmit = (data) => {
    Object.assign(formData, data)
    handleSubmit()
  }

  // 表单重置处理（由SimpleForm触发）
  const handleFormReset = () => {
    handleReset()
  }

  // 字段变化处理
  const handleFieldChange = (prop, value) => {
    console.log(`[OrderCreate] Field change: ${prop}`, value)

    // 更新表单数据
    if (typeof prop === 'object') {
      // 如果传入的是对象，批量更新
      Object.assign(formData, prop)
    } else {
      // 单个字段更新
      formData[prop] = value

      // 当商品改变时，自动填充商品名称
      if (prop === 'product_id') {
        formData.product_name = productNameMap[value] || ''
      }
    }
  }

  // 返回模板需要的所有数据和方法
  return {
    // 图标组件
    ArrowLeft,
    Refresh,
    Check,
    // 表单引用
    formRef,
    // 响应式数据
    submitting,
    pageTitle,
    formData,
    formConfig,
    // 方法
    handleBack,
    handleReset,
    handleSubmit,
    handleFormSubmit,
    handleFormReset,
    handleFieldChange
  }
}
