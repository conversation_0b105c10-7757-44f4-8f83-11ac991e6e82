import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { orderAPI } from '@/api'

/**
 * 订单列表页面组合式函数
 * @returns {Object} 返回模板需要的所有数据和方法
 */
export function useOrderList() {
  const router = useRouter()

  // 分页状态
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const loading = ref(false)

  // 搜索表单数据
  const searchFormData = reactive({
    trade_no: '',
    nickname: '',
    product_name: '',
    status: ''
  })

  // 表格配置
  const tableConfig = {
    columns: [
      { prop: 'id', label: 'ID', width: 80 },
      { prop: 'trade_no', label: '订单号', width: 180 },
      { prop: 'nickname', label: '用户昵称', width: 120 },
      { prop: 'product_name', label: '商品名称', width: 200 },
      { 
        prop: 'status', 
        label: '订单状态',
        type: 'tag',
        width: 100,
        tagMap: {
          '0': { type: 'warning', text: '待处理' },
          '1': { type: 'success', text: '已完成' },
          '2': { type: 'danger', text: '已取消' }
        }
      },
      { prop: 'use_time', label: '使用时间', type: 'datetime', width: 160 },
      { prop: 'remark', label: '备注', width: 150 },
      { prop: 'create_time', label: '创建时间', type: 'datetime', width: 160 },
      { prop: 'update_time', label: '更新时间', type: 'datetime', width: 160 }
    ],
    actions: [
      { type: 'view', label: '查看', style: 'text' },
      { type: 'edit', label: '编辑', style: 'primary' },
      { type: 'delete', label: '删除', style: 'danger' }
    ],
    pagination: true,
    selection: true
  }

  // 表格数据
  const tableData = ref([])

  // 模拟订单数据
  const mockOrders = [
    {
      id: 1,
      trade_no: '20250506150051410',
      nickname: '昵称',
      product_id: 20,
      product_name: '高级瑜伽课程',
      product_time_id: 16,
      status: '0',
      use_time: '2025-04-18 18:26:39',
      remark: '谁谁谁',
      unionid: '123456',
      user_id: 1,
      create_time: '2025-05-06 15:00:52',
      update_time: '2025-05-06 15:00:52',
      attach: null
    },
    {
      id: 2,
      trade_no: '20250507160052411',
      nickname: '张三',
      product_id: 21,
      product_name: '初级瑜伽课程',
      product_time_id: 17,
      status: '1',
      use_time: '2025-04-19 10:30:00',
      remark: '初学者',
      unionid: '123457',
      user_id: 2,
      create_time: '2025-05-07 16:00:52',
      update_time: '2025-05-07 18:30:15',
      attach: null
    },
    {
      id: 3,
      trade_no: '20250508170053412',
      nickname: '李四',
      product_id: 22,
      product_name: '冥想课程',
      product_time_id: 18,
      status: '2',
      use_time: '2025-04-20 14:00:00',
      remark: '压力大需要放松',
      unionid: '123458',
      user_id: 3,
      create_time: '2025-05-08 17:00:53',
      update_time: '2025-05-08 19:45:20',
      attach: null
    },
    {
      id: 4,
      trade_no: '20250509180054413',
      nickname: '王五',
      product_id: 23,
      product_name: '普拉提课程',
      product_time_id: 19,
      status: '0',
      use_time: '2025-04-21 16:15:00',
      remark: '想要塑形',
      unionid: '123459',
      user_id: 4,
      create_time: '2025-05-09 18:00:54',
      update_time: '2025-05-09 18:00:54',
      attach: null
    },
    {
      id: 5,
      trade_no: '20250510190055414',
      nickname: '赵六',
      product_id: 24,
      product_name: '舞蹈课程',
      product_time_id: 20,
      status: '1',
      use_time: '2025-04-22 19:30:00',
      remark: '喜欢跳舞',
      unionid: '123460',
      user_id: 5,
      create_time: '2025-05-10 19:00:55',
      update_time: '2025-05-10 21:15:30',
      attach: null
    }
  ]

  // 获取订单列表
  const fetchOrderList = async () => {
    loading.value = true
    try {
      const params = {
        page: currentPage.value,
        limit: pageSize.value,
        ...searchFormData
      }

      const response = await orderAPI.getOrderList(params)

      if (response && response.data) {
        tableData.value = response.data.list || []
        total.value = response.data.total || 0
      } else {
        // 如果API返回格式不同，使用备用逻辑
        tableData.value = response.list || response || []
        total.value = response.total || tableData.value.length
      }

    } catch (error) {
      console.error('获取订单列表失败:', error)
      ElMessage.error('获取订单列表失败，使用模拟数据')

      // 如果API失败，使用模拟数据作为备用
      let filteredData = [...mockOrders]

      if (searchFormData.trade_no) {
        filteredData = filteredData.filter(order =>
          order.trade_no.includes(searchFormData.trade_no)
        )
      }

      if (searchFormData.nickname) {
        filteredData = filteredData.filter(order =>
          order.nickname.includes(searchFormData.nickname)
        )
      }

      if (searchFormData.product_name) {
        filteredData = filteredData.filter(order =>
          order.product_name.includes(searchFormData.product_name)
        )
      }

      if (searchFormData.status) {
        filteredData = filteredData.filter(order =>
          order.status === searchFormData.status
        )
      }

      // 模拟分页
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value

      tableData.value = filteredData.slice(start, end)
      total.value = filteredData.length
    } finally {
      loading.value = false
    }
  }

  // 搜索订单
  const handleSearch = () => {
    currentPage.value = 1
    fetchOrderList()
    ElMessage.success('搜索完成')
  }

  // 重置搜索
  const handleReset = () => {
    Object.assign(searchFormData, {
      trade_no: '',
      nickname: '',
      product_name: '',
      status: ''
    })
    currentPage.value = 1
    fetchOrderList()
    ElMessage.info('已重置搜索条件')
  }

  // 新增订单
  const handleAdd = () => {
    router.push('/admin/order-list/order-create')
  }

  // 表格操作处理
  const handleTableAction = ({ type, row }) => {
    switch (type) {
      case 'view':
        ElMessage.info(`查看订单：${row.trade_no}`)
        // router.push(`/admin/order-edit/${row.id}?mode=view`)
        break
      case 'edit':
        ElMessage.success(`编辑订单：${row.trade_no}`)
        // router.push(`/admin/order-edit/${row.id}`)
        break
      case 'delete':
        handleDelete(row)
        break
      default:
        ElMessage.warning(`未知操作：${type}`)
    }
  }

  // 删除订单
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除订单 "${row.trade_no}" 吗？`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await orderAPI.deleteOrder(row.id)

      ElMessage.success(`订单 "${row.trade_no}" 删除成功`)
      fetchOrderList()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除订单失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }

  // 选择变化
  const handleSelectionChange = (selection) => {
    console.log('选中的订单：', selection)
    if (selection.length > 0) {
      ElMessage.info(`已选中 ${selection.length} 个订单`)
    }
  }

  // 分页改变
  const handlePageChange = (page) => {
    currentPage.value = page
    fetchOrderList()
  }

  // 页面大小改变
  const handleSizeChange = (size) => {
    pageSize.value = size
    currentPage.value = 1
    fetchOrderList()
  }

  // 页面加载时获取数据
  onMounted(() => {
    fetchOrderList()
  })

  // 返回模板需要的所有数据和方法
  return {
    // 图标组件
    Plus,
    Search,
    Refresh,
    // 响应式数据
    searchFormData,
    tableData,
    tableConfig,
    loading,
    total,
    currentPage,
    pageSize,
    // 方法
    handleAdd,
    handleSearch,
    handleReset,
    handleTableAction,
    handleSelectionChange,
    handlePageChange,
    handleSizeChange
  }
}
