<template>
  <div class="api-test-container">
    <el-page-header @back="$router.back()" title="API连接测试">
      <template #content>
        <span class="text-large font-600 mr-3">API接口测试</span>
      </template>
    </el-page-header>

    <div class="test-content">
      <el-card class="test-card">
        <template #header>
          <div class="card-header">
            <span>API连接状态</span>
            <el-button type="primary" @click="testAllAPIs" :loading="testing">
              <el-icon><Refresh /></el-icon>
              测试所有API
            </el-button>
          </div>
        </template>

        <div class="api-list">
          <div v-for="api in apiTests" :key="api.name" class="api-item">
            <div class="api-info">
              <div class="api-name">{{ api.name }}</div>
              <div class="api-url">{{ api.url }}</div>
            </div>
            <div class="api-actions">
              <el-button 
                size="small" 
                @click="testSingleAPI(api)"
                :loading="api.testing"
              >
                测试
              </el-button>
              <el-tag 
                :type="api.status === 'success' ? 'success' : api.status === 'error' ? 'danger' : 'info'"
                size="small"
              >
                {{ api.statusText }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="log-card" v-if="logs.length > 0">
        <template #header>
          <div class="card-header">
            <span>测试日志</span>
            <el-button size="small" @click="clearLogs">清空日志</el-button>
          </div>
        </template>

        <div class="log-list">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            <div class="log-time">{{ log.time }}</div>
            <div class="log-content" :class="log.type">{{ log.message }}</div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import { orderAPI, configAPI, productAPI, adminUserAPI } from '@/api'

// 测试状态
const testing = ref(false)
const logs = ref([])

// API测试配置
const apiTests = ref([
  {
    name: '订单列表API',
    url: '/admin/order/list',
    method: orderAPI.getOrderList,
    params: { page: 1, limit: 5 },
    testing: false,
    status: 'pending',
    statusText: '待测试'
  },
  {
    name: '配置列表API',
    url: '/admin/config/list',
    method: configAPI.getConfigList,
    params: { page: 1, limit: 5 },
    testing: false,
    status: 'pending',
    statusText: '待测试'
  },
  {
    name: '商品列表API',
    url: '/admin/product/list',
    method: productAPI.getProductList,
    params: { page: 1, limit: 5 },
    testing: false,
    status: 'pending',
    statusText: '待测试'
  },
  {
    name: '后台用户列表API',
    url: '/admin/admin/list',
    method: adminUserAPI.getAdminUserList,
    params: { page: 1, limit: 5 },
    testing: false,
    status: 'pending',
    statusText: '待测试'
  }
])

// 添加日志
const addLog = (message, type = 'info') => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 清空日志
const clearLogs = () => {
  logs.value = []
}

// 测试单个API
const testSingleAPI = async (api) => {
  api.testing = true
  api.status = 'pending'
  api.statusText = '测试中...'
  
  addLog(`开始测试: ${api.name}`)
  
  try {
    const startTime = Date.now()
    const response = await api.method(api.params)
    const endTime = Date.now()
    const duration = endTime - startTime
    
    api.status = 'success'
    api.statusText = `成功 (${duration}ms)`
    
    addLog(`✅ ${api.name} 测试成功，耗时: ${duration}ms`, 'success')
    addLog(`响应数据: ${JSON.stringify(response).substring(0, 100)}...`, 'info')
    
  } catch (error) {
    api.status = 'error'
    api.statusText = '失败'
    
    addLog(`❌ ${api.name} 测试失败: ${error.message}`, 'error')
    console.error(`API测试失败 [${api.name}]:`, error)
  } finally {
    api.testing = false
  }
}

// 测试所有API
const testAllAPIs = async () => {
  testing.value = true
  addLog('🚀 开始测试所有API接口...')
  
  for (const api of apiTests.value) {
    await testSingleAPI(api)
    // 添加延迟避免请求过于频繁
    await new Promise(resolve => setTimeout(resolve, 500))
  }
  
  testing.value = false
  addLog('🎉 所有API测试完成')
}

onMounted(() => {
  addLog('📋 API测试页面已加载，点击"测试所有API"开始测试')
})
</script>

<style scoped>
.api-test-container {
  padding: 20px;
}

.test-content {
  margin-top: 20px;
}

.test-card, .log-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.api-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.api-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.api-info {
  flex: 1;
}

.api-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.api-url {
  font-size: 12px;
  color: #909399;
  font-family: 'Courier New', monospace;
}

.api-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.log-list {
  max-height: 400px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 8px;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.log-time {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  font-family: 'Courier New', monospace;
}

.log-content {
  flex: 1;
  font-size: 14px;
  word-break: break-all;
}

.log-content.success {
  color: #67c23a;
}

.log-content.error {
  color: #f56c6c;
}

.log-content.info {
  color: #606266;
}
</style>
