<template>
  <div class="component-demo-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">组件演示</h1>
      <p class="page-description">展示各种可复用组件的功能和用法</p>
    </div>

    <!-- 弹窗组件演示 -->
    <el-card class="demo-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h2>ModalDialog 弹窗组件</h2>
          <span class="card-description">支持多种内容类型的通用弹窗组件</span>
        </div>
      </template>

      <div class="demo-section">
        <h3>基础演示</h3>
        <div class="demo-buttons">
          <el-button type="primary" @click="openTextDialog">
            <el-icon><Document /></el-icon>
            文本弹窗
          </el-button>
          <el-button type="success" @click="openHtmlDialog">
            <el-icon><Notebook /></el-icon>
            HTML弹窗
          </el-button>
          <el-button type="warning" @click="openUrlDialog">
            <el-icon><Link /></el-icon>
            URL弹窗
          </el-button>
          <el-button type="info" @click="openImageDialog">
            <el-icon><Picture /></el-icon>
            图片弹窗
          </el-button>
        </div>
      </div>

      <div class="demo-section">
        <h3>高级用法</h3>
        <div class="demo-buttons">
          <el-button type="primary" plain @click="openFullscreenDialog">
            <el-icon><FullScreen /></el-icon>
            全屏弹窗
          </el-button>
          <el-button type="success" plain @click="openCustomDialog">
            <el-icon><Setting /></el-icon>
            自定义配置
          </el-button>
          <el-button type="danger" plain @click="openNoFooterDialog">
            <el-icon><Hide /></el-icon>
            无底部按钮
          </el-button>
        </div>
      </div>

      <div class="demo-section">
        <h3>配置说明</h3>
        <el-table :data="configTableData" border style="width: 100%">
          <el-table-column prop="prop" label="属性" width="150" />
          <el-table-column prop="type" label="类型" width="120" />
          <el-table-column prop="default" label="默认值" width="120" />
          <el-table-column prop="description" label="说明" />
        </el-table>
      </div>
    </el-card>

    <!-- 表格组件演示 -->
    <el-card class="demo-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h2>ConfigurableTable 表格组件</h2>
          <span class="card-description">可配置的数据表格组件</span>
        </div>
      </template>

      <div class="demo-section">
        <h3>基础表格</h3>
        <ConfigurableTable 
          :config="basicTableConfig"
          :data="sampleTableData"
          :loading="false"
        />
      </div>
    </el-card>

    <!-- 表单组件演示 -->
    <el-card class="demo-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h2>ConfigurableForm 表单组件</h2>
          <span class="card-description">可配置的动态表单组件</span>
        </div>
      </template>

      <div class="demo-section">
        <h3>基础表单</h3>
        <ConfigurableForm 
          :config="basicFormConfig"
          :model="formModel"
          @submit="handleFormSubmit"
          @field-change="handleFieldChange"
        />
      </div>
    </el-card>

    <!-- 弹窗组件实例 -->
    <ModalDialog
      v-model="dialogVisible"
      :title="dialogConfig.title"
      :type="dialogConfig.type"
      :content="dialogConfig.content"
      :width="dialogConfig.width"
      :content-height="dialogConfig.contentHeight"
      :show-footer="dialogConfig.showFooter"
      :fullscreen="dialogConfig.fullscreen"
      @confirm="handleDialogConfirm"
      @cancel="handleDialogCancel"
    />
  </div>
</template>

<script setup>
import { useComponentDemo } from './ComponentDemo.js'
import ConfigurableTable from '@/components/table/ConfigurableTable.vue'
import ConfigurableForm from '@/components/form/ConfigurableForm.vue'
import ModalDialog from '@/components/dialog/ModalDialog.vue'

// 使用组件演示组合式函数
const {
  // 图标组件
  Document,
  Notebook,
  Link,
  Picture,
  FullScreen,
  Setting,
  Hide,
  // 响应式数据
  dialogVisible,
  dialogConfig,
  configTableData,
  basicTableConfig,
  sampleTableData,
  basicFormConfig,
  formModel,
  // 方法
  openTextDialog,
  openHtmlDialog,
  openUrlDialog,
  openImageDialog,
  openFullscreenDialog,
  openCustomDialog,
  openNoFooterDialog,
  handleDialogConfirm,
  handleDialogCancel,
  handleFormSubmit,
  handleFieldChange
} = useComponentDemo()
</script>

<style scoped>
.component-demo-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 10px 0;
}

.page-description {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.demo-card {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.card-description {
  font-size: 14px;
  color: #909399;
}

.demo-section {
  margin-bottom: 30px;
}

.demo-section:last-child {
  margin-bottom: 0;
}

.demo-section h3 {
  font-size: 16px;
  font-weight: 500;
  color: #409eff;
  margin: 0 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.demo-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.demo-buttons .el-button {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .component-demo-page {
    padding: 15px;
  }
  
  .demo-buttons {
    flex-direction: column;
  }
  
  .demo-buttons .el-button {
    width: 100%;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
</style>
