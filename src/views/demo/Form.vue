<template>
  <div class="form-page">
    <el-card class="form-container">
      <template #header>
        <div class="form-header">
          <h3>{{ currentConfig.title || '表单页面' }}</h3>
          <el-button-group>
            <el-button @click="switchConfig('basic')">基础表单</el-button>
            <el-button @click="switchConfig('advanced')">高级表单</el-button>
            <el-button @click="switchConfig('upload')">扩展组件</el-button>
          </el-button-group>
        </div>
      </template>

      <!-- 配置化表单 -->
      <ConfigurableForm
        ref="formRef"
        :config="currentConfig"
        :model="formData"
        @submit="handleSubmit"
        @reset="handleReset"
        @field-change="handleFieldChange"
      />
    </el-card>

    <!-- 示例配置展示 -->
    <el-card class="config-display" style="margin-top: 20px;">
      <template #header>
        <h4>当前表单配置 (JSON)</h4>
      </template>
      <el-input
        v-model="configJson"
        type="textarea"
        :rows="10"
        readonly
        placeholder="表单配置 JSON"
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import ConfigurableForm from '@/components/form/ConfigurableForm.vue'
import { request } from '@/api'

// 表单引用
const formRef = ref()

// 当前表单配置类型
const currentConfigType = ref('basic')

// 表单数据
const formData = reactive({})

// 基础表单配置
const basicFormConfig = {
  title: '基础信息表单',
  labelWidth: '120px',
  fields: [
    {
      prop: 'name',
      label: '姓名',
      type: 'input',
      placeholder: '请输入姓名',
      rules: [
        { required: true, message: '请输入姓名', trigger: 'blur' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
      ]
    },
    {
      prop: 'email',
      label: '邮箱',
      type: 'input',
      placeholder: '请输入邮箱地址',
      rules: [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ]
    },
    {
      prop: 'phone',
      label: '手机号',
      type: 'input',
      placeholder: '请输入手机号',
      rules: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
      ]
    },
    {
      prop: 'gender',
      label: '性别',
      type: 'radio',
      options: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' }
      ],
      rules: [{ required: true, message: '请选择性别', trigger: 'change' }]
    },
    {
      prop: 'city',
      label: '城市',
      type: 'select',
      placeholder: '请选择城市',
      options: [
        { label: '北京', value: 'beijing' },
        { label: '上海', value: 'shanghai' },
        { label: '广州', value: 'guangzhou' },
        { label: '深圳', value: 'shenzhen' }
      ],
      rules: [{ required: true, message: '请选择城市', trigger: 'change' }]
    }
  ],
  actions: [
    { type: 'submit', label: '提交', style: 'primary' },
    { type: 'reset', label: '重置', style: 'default' }
  ]
}

// 高级表单配置
const advancedFormConfig = {
  title: '高级配置表单',
  labelWidth: '140px',
  fields: [
    {
      prop: 'title',
      label: '文章标题',
      type: 'input',
      placeholder: '请输入文章标题',
      rules: [{ required: true, message: '请输入文章标题', trigger: 'blur' }]
    },
    {
      prop: 'category',
      label: '文章分类',
      type: 'select',
      placeholder: '请选择分类',
      options: [
        { label: '技术', value: 'tech' },
        { label: '生活', value: 'life' },
        { label: '随笔', value: 'essay' }
      ],
      rules: [{ required: true, message: '请选择分类', trigger: 'change' }]
    },
    {
      prop: 'tags',
      label: '标签',
      type: 'checkbox',
      options: [
        { label: 'Vue', value: 'vue' },
        { label: 'React', value: 'react' },
        { label: 'Node.js', value: 'nodejs' },
        { label: 'JavaScript', value: 'javascript' }
      ]
    },
    {
      prop: 'publishDate',
      label: '发布日期',
      type: 'date',
      placeholder: '选择发布日期',
      rules: [{ required: true, message: '请选择发布日期', trigger: 'change' }]
    },
    {
      prop: 'content',
      label: '文章内容',
      type: 'textarea',
      placeholder: '请输入文章内容',
      rows: 4,
      rules: [{ required: true, message: '请输入文章内容', trigger: 'blur' }]
    },
    {
      prop: 'published',
      label: '立即发布',
      type: 'switch'
    }
  ],
  actions: [
    { type: 'submit', label: '保存', style: 'primary' },
    { type: 'reset', label: '清空', style: 'default' }
  ]
}

// 文件上传表单配置
const uploadFormConfig = {
  title: '扩展组件表单',
  labelWidth: '120px',
  fields: [
    {
      prop: 'userName',
      label: '用户名',
      type: 'input',
      placeholder: '请输入用户名',
      rules: [{ required: true, message: '请输入用户名', trigger: 'blur' }]
    },
    {
      prop: 'avatar',
      label: '头像上传',
      type: 'upload',
      accept: 'image/*',
      listType: 'picture-card',
      limit: 1,
      action: '/api/upload',
      rules: [{ required: true, message: '请上传头像', trigger: 'change' }]
    },
    {
      prop: 'documents',
      label: '文档上传',
      type: 'upload',
      accept: '.pdf,.doc,.docx',
      multiple: true,
      limit: 3,
      action: '/api/upload'
    },
    {
      prop: 'region',
      label: '所在地区',
      type: 'cascader',
      placeholder: '请选择地区',
      options: [
        {
          value: 'beijing',
          label: '北京',
          children: [
            { value: 'haidian', label: '海淀区' },
            { value: 'chaoyang', label: '朝阳区' },
            { value: 'xicheng', label: '西城区' }
          ]
        },
        {
          value: 'shanghai',
          label: '上海',
          children: [
            { value: 'huangpu', label: '黄浦区' },
            { value: 'xuhui', label: '徐汇区' },
            { value: 'changning', label: '长宁区' }
          ]
        }
      ]
    },
    {
      prop: 'birthday',
      label: '生日',
      type: 'date',
      placeholder: '请选择生日'
    },
    {
      prop: 'appointmentTime',
      label: '预约时间',
      type: 'datetime',
      placeholder: '请选择预约时间'
    },
    {
      prop: 'workPeriod',
      label: '工作时间',
      type: 'daterange',
      placeholder: '请选择工作时间范围'
    },
    {
      prop: 'content',
      label: '详细内容',
      type: 'richtext',
      placeholder: '请输入详细内容...'
    },
    {
      prop: 'description',
      label: '简要描述',
      type: 'textarea',
      placeholder: '请输入简要描述',
      rows: 3
    }
  ],
  actions: [
    { type: 'submit', label: '提交', style: 'primary' },
    { type: 'reset', label: '重置', style: 'default' }
  ]
}

// 表单配置映射
const formConfigs = {
  basic: basicFormConfig,
  advanced: advancedFormConfig,
  upload: uploadFormConfig
}

// 当前表单配置
const currentConfig = computed(() => formConfigs[currentConfigType.value])

// 配置 JSON 字符串
const configJson = computed(() => 
  JSON.stringify(currentConfig.value, null, 2)
)

// 切换表单配置
const switchConfig = (type) => {
  currentConfigType.value = type
  // 清空表单数据
  Object.keys(formData).forEach(key => {
    delete formData[key]
  })
  
  // 初始化上传字段为数组
  if (type === 'upload') {
    formData.avatar = []
    formData.documents = []
  }
}

// 处理表单提交
const handleSubmit = async (data) => {
  try {
    console.log('表单提交数据:', data)
    
    // 使用新的API系统
    // const response = await request.post('/form/submit', data)
    
    // 模拟提交成功
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('表单提交成功！')
  } catch (error) {
    console.error('表单提交失败:', error)
    ElMessage.error('表单提交失败，请重试')
  }
}

// 处理表单重置
const handleReset = () => {
  Object.keys(formData).forEach(key => {
    delete formData[key]
  })
  
  // 重新初始化上传字段
  if (currentConfigType.value === 'upload') {
    formData.avatar = []
    formData.documents = []
  }
  
  ElMessage.info('表单已重置')
}

// 处理字段变化
const handleFieldChange = (prop, value) => {
  console.log(`字段 ${prop} 值变更为:`, value)
  formData[prop] = value
}
</script>

<style scoped>
.form-page {
  padding: 20px;
}

.form-container {
  max-width: 800px;
  margin: 0 auto;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-header h3 {
  margin: 0;
  color: #303133;
}

.config-display {
  max-width: 800px;
  margin: 0 auto;
}

:deep(.el-textarea__inner) {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-page {
    padding: 10px;
  }
  
  .form-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .form-header h3 {
    font-size: 18px;
  }
}
</style>
