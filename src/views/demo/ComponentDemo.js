import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Document, 
  Notebook, 
  Link, 
  Picture, 
  FullScreen, 
  Setting, 
  Hide 
} from '@element-plus/icons-vue'

/**
 * 组件演示页面组合式函数
 * @returns {Object} 返回模板需要的所有数据和方法
 */
export function useComponentDemo() {
  
  // 弹窗相关状态
  const dialogVisible = ref(false)
  const dialogConfig = reactive({
    title: '弹窗标题',
    type: 'text',
    content: '',
    width: '50%',
    contentHeight: 'auto',
    showFooter: true,
    fullscreen: false
  })

  // 配置说明表格数据
  const configTableData = [
    {
      prop: 'type',
      type: 'String',
      default: 'slot',
      description: '弹窗内容类型：text(文本) | html(HTML) | url(网页) | image(图片) | video(视频) | component(组件) | slot(插槽)'
    },
    {
      prop: 'title',
      type: 'String',
      default: '弹窗标题',
      description: '弹窗标题文本'
    },
    {
      prop: 'content',
      type: 'String | Object',
      default: '',
      description: '弹窗内容，根据type类型显示不同内容'
    },
    {
      prop: 'width',
      type: 'String | Number',
      default: '50%',
      description: '弹窗宽度'
    },
    {
      prop: 'contentHeight',
      type: 'String',
      default: 'auto',
      description: '内容区域高度'
    },
    {
      prop: 'showFooter',
      type: 'Boolean',
      default: true,
      description: '是否显示底部按钮'
    },
    {
      prop: 'fullscreen',
      type: 'Boolean',
      default: false,
      description: '是否全屏显示'
    },
    {
      prop: 'draggable',
      type: 'Boolean',
      default: false,
      description: '是否可拖拽'
    }
  ]

  // 示例表格配置
  const basicTableConfig = reactive({
    columns: [
      {
        prop: 'id',
        label: 'ID',
        width: 80
      },
      {
        prop: 'name',
        label: '名称',
        width: 120
      },
      {
        prop: 'status',
        label: '状态',
        width: 100,
        type: 'tag',
        tagMap: {
          'active': { type: 'success', text: '活跃' },
          'inactive': { type: 'danger', text: '非活跃' }
        }
      },
      {
        prop: 'createTime',
        label: '创建时间',
        width: 160,
        type: 'datetime'
      }
    ],
    actions: [
      {
        type: 'view',
        label: '查看',
        style: 'text',
        icon: 'View'
      },
      {
        type: 'edit',
        label: '编辑',
        style: 'primary',
        icon: 'Edit'
      }
    ],
    pagination: {
      show: true,
      currentPage: 1,
      pageSize: 10,
      total: 3,
      pageSizes: [10, 20, 50]
    },
    selection: true,
    border: true,
    stripe: true
  })

  // 示例表格数据
  const sampleTableData = [
    {
      id: 1,
      name: '演示项目1',
      status: 'active',
      createTime: '2024-06-19 10:00:00'
    },
    {
      id: 2,
      name: '演示项目2',
      status: 'inactive',
      createTime: '2024-06-18 15:30:00'
    },
    {
      id: 3,
      name: '演示项目3',
      status: 'active',
      createTime: '2024-06-17 09:20:00'
    }
  ]

  // 示例表单配置
  const basicFormConfig = reactive({
    labelWidth: '120px',
    fields: [
      {
        prop: 'name',
        label: '项目名称',
        type: 'input',
        placeholder: '请输入项目名称',
        rules: [
          { required: true, message: '请输入项目名称', trigger: 'blur' }
        ]
      },
      {
        prop: 'description',
        label: '项目描述',
        type: 'textarea',
        placeholder: '请输入项目描述',
        rows: 3
      },
      {
        prop: 'type',
        label: '项目类型',
        type: 'select',
        placeholder: '请选择项目类型',
        options: [
          { label: 'Web应用', value: 'web' },
          { label: '移动应用', value: 'mobile' },
          { label: '桌面应用', value: 'desktop' }
        ]
      },
      {
        prop: 'status',
        label: '项目状态',
        type: 'radio',
        options: [
          { label: '活跃', value: 'active' },
          { label: '非活跃', value: 'inactive' }
        ]
      },
      {
        prop: 'startDate',
        label: '开始日期',
        type: 'date',
        placeholder: '请选择开始日期'
      },
      {
        prop: 'isPublic',
        label: '是否公开',
        type: 'switch'
      }
    ],
    actions: [
      {
        type: 'submit',
        label: '提交',
        style: 'primary'
      },
      {
        type: 'reset',
        label: '重置',
        style: 'default'
      }
    ]
  })

  // 表单数据模型
  const formModel = reactive({
    name: '',
    description: '',
    type: '',
    status: 'active',
    startDate: '',
    isPublic: false
  })

  // 打开文本弹窗
  const openTextDialog = () => {
    dialogConfig.title = '文本内容弹窗'
    dialogConfig.type = 'text'
    dialogConfig.content = `这是一个文本内容的弹窗演示。

支持多行文本显示，包括：
- 换行符处理
- 自动换行
- 文本格式保持

您可以在这里显示任意的纯文本内容，比如用户协议、帮助文档、系统公告等。

该组件特别适合用于：
✓ 显示长文本内容
✓ 系统通知和公告
✓ 帮助文档展示
✓ 用户协议条款
✓ 操作说明指引`
    dialogConfig.width = '600px'
    dialogConfig.contentHeight = '400px'
    dialogConfig.showFooter = true
    dialogConfig.fullscreen = false
    dialogVisible.value = true
  }

  // 打开HTML弹窗
  const openHtmlDialog = () => {
    dialogConfig.title = 'HTML内容弹窗'
    dialogConfig.type = 'html'
    dialogConfig.content = `
      <div style="padding: 20px;">
        <h2 style="color: #409eff; margin-top: 0;">HTML内容演示</h2>
        <p>这是一个支持<strong>HTML标签</strong>的弹窗内容。</p>
        
        <h3 style="color: #67c23a;">功能特性：</h3>
        <ul>
          <li>支持<em>各种HTML标签</em></li>
          <li>支持<code style="background: #f56c6c; color: white; padding: 2px 4px; border-radius: 3px;">代码高亮</code></li>
          <li>支持<a href="#" onclick="alert('链接点击演示')" style="color: #409eff;">交互链接</a></li>
          <li>支持表格、图片等复杂内容</li>
        </ul>

        <blockquote style="border-left: 4px solid #409eff; padding-left: 16px; margin: 16px 0; color: #909399;">
          这是一个引用块，可以用来突出重要信息。组件会自动处理HTML内容的安全性。
        </blockquote>

        <h3 style="color: #e6a23c;">数据展示：</h3>
        <table style="width: 100%; border-collapse: collapse; margin: 16px 0;">
          <thead>
            <tr style="background: #f5f7fa;">
              <th style="border: 1px solid #dcdfe6; padding: 8px 12px; text-align: left;">功能</th>
              <th style="border: 1px solid #dcdfe6; padding: 8px 12px; text-align: left;">状态</th>
              <th style="border: 1px solid #dcdfe6; padding: 8px 12px; text-align: left;">说明</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style="border: 1px solid #dcdfe6; padding: 8px 12px;">文本显示</td>
              <td style="border: 1px solid #dcdfe6; padding: 8px 12px;"><span style="color: #67c23a;">✅ 支持</span></td>
              <td style="border: 1px solid #dcdfe6; padding: 8px 12px;">完美支持</td>
            </tr>
            <tr>
              <td style="border: 1px solid #dcdfe6; padding: 8px 12px;">HTML渲染</td>
              <td style="border: 1px solid #dcdfe6; padding: 8px 12px;"><span style="color: #67c23a;">✅ 支持</span></td>
              <td style="border: 1px solid #dcdfe6; padding: 8px 12px;">安全渲染</td>
            </tr>
            <tr>
              <td style="border: 1px solid #dcdfe6; padding: 8px 12px;">样式自定义</td>
              <td style="border: 1px solid #dcdfe6; padding: 8px 12px;"><span style="color: #67c23a;">✅ 支持</span></td>
              <td style="border: 1px solid #dcdfe6; padding: 8px 12px;">内置样式</td>
            </tr>
          </tbody>
        </table>
      </div>
    `
    dialogConfig.width = '800px'
    dialogConfig.contentHeight = '500px'
    dialogConfig.showFooter = true
    dialogConfig.fullscreen = false
    dialogVisible.value = true
  }

  // 打开URL弹窗
  const openUrlDialog = () => {
    dialogConfig.title = 'Element Plus 官方文档'
    dialogConfig.type = 'url'
    dialogConfig.content = 'https://element-plus.org/zh-CN/'
    dialogConfig.width = '90%'
    dialogConfig.contentHeight = '600px'
    dialogConfig.showFooter = false
    dialogConfig.fullscreen = false
    dialogVisible.value = true
  }

  // 打开图片弹窗
  const openImageDialog = () => {
    dialogConfig.title = '图片展示'
    dialogConfig.type = 'image'
    dialogConfig.content = 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    dialogConfig.width = '600px'
    dialogConfig.contentHeight = '400px'
    dialogConfig.showFooter = true
    dialogConfig.fullscreen = false
    dialogVisible.value = true
  }

  // 打开全屏弹窗
  const openFullscreenDialog = () => {
    dialogConfig.title = '全屏模式演示'
    dialogConfig.type = 'html'
    dialogConfig.content = `
      <div style="padding: 40px; text-align: center;">
        <h1 style="color: #409eff; font-size: 36px; margin-bottom: 30px;">全屏弹窗演示</h1>
        <p style="font-size: 18px; line-height: 1.8; color: #606266; max-width: 800px; margin: 0 auto;">
          全屏模式适合展示大量内容或需要用户专注操作的场景。
          在全屏模式下，弹窗会占据整个浏览器窗口，提供更好的视觉体验。
        </p>
        <div style="margin-top: 50px;">
          <div style="display: inline-block; padding: 20px; background: #f5f7fa; border-radius: 8px; margin: 10px;">
            <h3 style="color: #67c23a; margin-top: 0;">适用场景</h3>
            <ul style="text-align: left; color: #606266;">
              <li>复杂表单填写</li>
              <li>数据详情展示</li>
              <li>图片/视频预览</li>
              <li>报表分析页面</li>
            </ul>
          </div>
          <div style="display: inline-block; padding: 20px; background: #f5f7fa; border-radius: 8px; margin: 10px;">
            <h3 style="color: #e6a23c; margin-top: 0;">交互特性</h3>
            <ul style="text-align: left; color: #606266;">
              <li>ESC键关闭</li>
              <li>点击遮罩关闭</li>
              <li>支持拖拽</li>
              <li>响应式布局</li>
            </ul>
          </div>
        </div>
      </div>
    `
    dialogConfig.width = '100%'
    dialogConfig.contentHeight = '100%'
    dialogConfig.showFooter = true
    dialogConfig.fullscreen = true
    dialogVisible.value = true
  }

  // 打开自定义配置弹窗
  const openCustomDialog = () => {
    dialogConfig.title = '自定义配置演示'
    dialogConfig.type = 'html'
    dialogConfig.content = `
      <div style="padding: 20px;">
        <h3 style="color: #409eff;">组件配置灵活性</h3>
        <p>ModalDialog组件支持丰富的配置选项，可以满足各种业务场景需求：</p>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
          <div>
            <h4 style="color: #67c23a;">尺寸配置</h4>
            <ul style="font-size: 14px; color: #606266;">
              <li>width: 支持百分比和像素值</li>
              <li>contentHeight: 内容区域高度</li>
              <li>fullscreen: 全屏模式</li>
            </ul>
          </div>
          <div>
            <h4 style="color: #e6a23c;">交互配置</h4>
            <ul style="font-size: 14px; color: #606266;">
              <li>draggable: 是否可拖拽</li>
              <li>closeOnClickModal: 点击遮罩关闭</li>
              <li>closeOnPressEscape: ESC键关闭</li>
            </ul>
          </div>
        </div>

        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #409eff;">
          <h4 style="margin-top: 0; color: #409eff;">内容类型支持</h4>
          <p style="margin-bottom: 0; color: #606266;">
            text(纯文本) | html(富文本) | url(网页嵌入) | image(图片展示) | 
            video(视频播放) | component(Vue组件) | slot(自定义内容)
          </p>
        </div>
      </div>
    `
    dialogConfig.width = '700px'
    dialogConfig.contentHeight = '450px'
    dialogConfig.showFooter = true
    dialogConfig.fullscreen = false
    dialogVisible.value = true
  }

  // 打开无底部按钮弹窗
  const openNoFooterDialog = () => {
    dialogConfig.title = '无底部按钮模式'
    dialogConfig.type = 'html'
    dialogConfig.content = `
      <div style="padding: 20px; text-align: center;">
        <div style="font-size: 48px; color: #67c23a; margin-bottom: 20px;">✓</div>
        <h3 style="color: #67c23a; margin-bottom: 15px;">操作成功</h3>
        <p style="color: #606266; margin-bottom: 30px;">
          某些场景下不需要底部操作按钮，比如纯展示内容、成功提示等。
          用户可以通过点击右上角关闭按钮或按ESC键关闭弹窗。
        </p>
        <div style="background: #f0f9ff; padding: 15px; border-radius: 6px; border: 1px solid #b3d8ff;">
          <p style="margin: 0; color: #409eff; font-size: 14px;">
            💡 提示：这种模式适合纯信息展示或不需要用户确认的场景
          </p>
        </div>
      </div>
    `
    dialogConfig.width = '500px'
    dialogConfig.contentHeight = '300px'
    dialogConfig.showFooter = false
    dialogConfig.fullscreen = false
    dialogVisible.value = true
  }

  // 弹窗确认
  const handleDialogConfirm = () => {
    ElMessage.success('弹窗确认操作')
    dialogVisible.value = false
  }

  // 弹窗取消
  const handleDialogCancel = () => {
    ElMessage.info('弹窗取消操作')
  }

  // 表单提交
  const handleFormSubmit = (formData) => {
    console.log('表单提交数据:', formData)
    ElMessage.success('表单提交成功')
  }

  // 表单字段变化
  const handleFieldChange = (prop, value) => {
    console.log('字段变化:', prop, value)
  }

  // 返回模板需要的所有数据和方法
  return {
    // 图标组件
    Document,
    Notebook,
    Link,
    Picture,
    FullScreen,
    Setting,
    Hide,
    // 响应式数据
    dialogVisible,
    dialogConfig,
    configTableData,
    basicTableConfig,
    sampleTableData,
    basicFormConfig,
    formModel,
    // 方法
    openTextDialog,
    openHtmlDialog,
    openUrlDialog,
    openImageDialog,
    openFullscreenDialog,
    openCustomDialog,
    openNoFooterDialog,
    handleDialogConfirm,
    handleDialogCancel,
    handleFormSubmit,
    handleFieldChange
  }
}
