<template>
  <div class="menu-demo-page">
    <el-card class="demo-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h2>菜单模式演示</h2>
          <p>演示不同的菜单显示模式和3级菜单功能</p>
        </div>
      </template>

      <!-- 菜单模式切换 -->
      <div class="mode-switcher">
        <h3>菜单模式切换</h3>
        <el-radio-group v-model="currentMode" @change="handleModeChange">
          <el-radio-button value="hierarchical">层级菜单（默认）</el-radio-button>
          <el-radio-button value="flat">扁平菜单（不折叠）</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 当前配置显示 -->
      <div class="config-display">
        <h3>当前菜单配置</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="菜单模式">
            {{ currentMode === 'flat' ? '扁平化菜单' : '层级菜单' }}
          </el-descriptions-item>
          <el-descriptions-item label="扁平化菜单">
            {{ menuConfig.mode?.flatMenu ? '启用' : '禁用' }}
          </el-descriptions-item>
          <el-descriptions-item label="3级菜单支持">
            {{ menuConfig.mode?.supportThreeLevel ? '启用' : '禁用' }}
          </el-descriptions-item>
          <el-descriptions-item label="唯一展开">
            {{ menuConfig.mode?.uniqueOpened ? '启用' : '禁用' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 菜单结构预览 -->
      <div class="menu-preview">
        <h3>菜单结构预览</h3>
        <div class="preview-container">
          <div class="menu-tree">
            <div v-for="menu in previewMenus" :key="menu.path" class="menu-item">
              <div class="menu-title">
                <el-icon><component :is="getIconComponent(menu.icon)" /></el-icon>
                {{ menu.title }}
                <el-tag v-if="menu.moduleTitle" size="small" type="info">
                  {{ menu.moduleTitle }}
                </el-tag>
              </div>
              
              <!-- 二级菜单 -->
              <div v-if="menu.children && menu.children.length > 0" class="menu-children">
                <div v-for="child in menu.children" :key="child.path" class="child-item">
                  <div class="child-title">
                    <el-icon><component :is="getIconComponent(child.icon)" /></el-icon>
                    {{ child.title }}
                  </div>
                  
                  <!-- 三级菜单 -->
                  <div v-if="child.children && child.children.length > 0" class="grandchild-container">
                    <div v-for="grandChild in child.children" :key="grandChild.path" class="grandchild-item">
                      <el-icon><component :is="getIconComponent(grandChild.icon)" /></el-icon>
                      {{ grandChild.title }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能说明 -->
      <div class="feature-description">
        <h3>功能说明</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="feature-card">
              <h4>层级菜单模式</h4>
              <ul>
                <li>支持折叠的子菜单</li>
                <li>支持3级菜单结构</li>
                <li>清晰的层级关系</li>
                <li>节省菜单空间</li>
              </ul>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="feature-card">
              <h4>扁平菜单模式</h4>
              <ul>
                <li>所有菜单项平铺显示</li>
                <li>无折叠，一目了然</li>
                <li>快速访问所有功能</li>
                <li>适合功能较少的系统</li>
              </ul>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button type="primary" @click="refreshMenu">
          <el-icon><Refresh /></el-icon>
          刷新菜单
        </el-button>
        <el-button @click="resetConfig">
          <el-icon><RefreshLeft /></el-icon>
          重置配置
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, markRaw } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  RefreshLeft,
  Operation,
  Edit,
  Grid,
  Monitor,
  User,
  Management,
  Goods,
  Setting,
  ShoppingCart,
  Lock,
  Sunny,
  Document,
  DocumentChecked,
  Guide,
  Notebook,
  TrendCharts,
  DataLine,
  DataBoard,
  PieChart,
  List,
  EditPen
} from '@element-plus/icons-vue'
import { getMenuConfig, setMenuMode, toggleFlatMenu, toggleThreeLevelMenu } from '@/config/menu.js'
import { generateMenus } from '@/router/modules/index.js'

// 当前菜单模式
const currentMode = ref('hierarchical')

// 菜单配置
const menuConfig = ref({
  mode: {
    flatMenu: false,
    supportThreeLevel: true,
    uniqueOpened: false
  }
})

// 预览菜单
const previewMenus = ref([])

// 处理模式切换
const handleModeChange = (mode) => {
  setMenuMode(mode)
  updateMenuConfig()
  updatePreviewMenus()
  ElMessage.success(`已切换到${mode === 'flat' ? '扁平' : '层级'}菜单模式`)
}

// 更新菜单配置
const updateMenuConfig = () => {
  menuConfig.value = getMenuConfig()
}

// 更新预览菜单
const updatePreviewMenus = () => {
  const menus = generateMenus(menuConfig.value.mode?.flatMenu || false)
  previewMenus.value = menus.slice(0, 3) // 只显示前3个菜单用于预览
}

// 刷新菜单
const refreshMenu = () => {
  updateMenuConfig()
  updatePreviewMenus()
  ElMessage.success('菜单已刷新')
}

// 重置配置
const resetConfig = () => {
  currentMode.value = 'hierarchical'
  setMenuMode('hierarchical')
  updateMenuConfig()
  updatePreviewMenus()
  ElMessage.success('配置已重置')
}

// 获取图标组件
const getIconComponent = (iconName) => {
  const iconMap = {
    'Edit': markRaw(Edit),
    'Grid': markRaw(Grid),
    'Management': markRaw(Management),
    'User': markRaw(User),
    'Monitor': markRaw(Monitor),
    'Operation': markRaw(Operation),
    'Goods': markRaw(Goods),
    'Setting': markRaw(Setting),
    'ShoppingCart': markRaw(ShoppingCart),
    'Lock': markRaw(Lock),
    'Sunny': markRaw(Sunny),
    'Document': markRaw(Document),
    'DocumentChecked': markRaw(DocumentChecked),
    'Guide': markRaw(Guide),
    'Notebook': markRaw(Notebook),
    'TrendCharts': markRaw(TrendCharts),
    'DataLine': markRaw(DataLine),
    'DataBoard': markRaw(DataBoard),
    'PieChart': markRaw(PieChart),
    'List': markRaw(List),
    'EditPen': markRaw(EditPen)
  }
  return iconMap[iconName] || markRaw(Operation)
}

// 页面挂载时初始化
onMounted(() => {
  updateMenuConfig()
  updatePreviewMenus()
})
</script>

<style scoped>
.menu-demo-page {
  padding: 20px;
}

.demo-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header h2 {
  margin: 0 0 8px 0;
  color: #1f2937;
}

.card-header p {
  margin: 0;
  color: #6b7280;
}

.mode-switcher,
.config-display,
.menu-preview,
.feature-description {
  margin-bottom: 32px;
}

.mode-switcher h3,
.config-display h3,
.menu-preview h3,
.feature-description h3 {
  margin-bottom: 16px;
  color: #374151;
}

.preview-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background: #f9fafb;
}

.menu-tree {
  max-height: 400px;
  overflow-y: auto;
}

.menu-item {
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.menu-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.menu-children {
  margin-left: 24px;
}

.child-item {
  margin-bottom: 8px;
  padding: 8px;
  background: #f3f4f6;
  border-radius: 4px;
}

.child-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.grandchild-container {
  margin-left: 20px;
}

.grandchild-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  margin-bottom: 4px;
  background: #e5e7eb;
  border-radius: 3px;
  font-size: 14px;
  color: #4b5563;
}

.feature-card {
  height: 100%;
}

.feature-card h4 {
  margin-bottom: 12px;
  color: #1f2937;
}

.feature-card ul {
  margin: 0;
  padding-left: 20px;
}

.feature-card li {
  margin-bottom: 8px;
  color: #6b7280;
}

.action-buttons {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.action-buttons .el-button {
  margin: 0 8px;
}
</style>
