<template>
  <div class="simple-demo-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">超简化组件使用示例</h1>
      <p class="page-description">只需配置JSON和回调函数即可使用</p>
    </div>

    <!-- 简化表格示例 -->
    <el-card class="demo-card" shadow="never">
      <template #header>
        <h2>SimpleTable - 超简化表格</h2>
      </template>
      
      <div class="demo-section">
        <h3>用法示例</h3>
        <pre class="code-block">{{ tableUsageCode }}</pre>
        
        <h3>实际效果</h3>
        <SimpleTable 
          :data="tableData"
          :config="tableConfig"
          :loading="tableLoading"
          :total="100"
          :current-page="currentPage"
          :page-size="pageSize"
          @action="handleTableAction"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>

    <!-- 简化表单示例 -->
    <el-card class="demo-card" shadow="never">
      <template #header>
        <h2>SimpleForm - 超简化表单</h2>
      </template>

      <div class="demo-section">
        <h3>基础表单用法</h3>
        <pre class="code-block">{{ formUsageCode }}</pre>

        <h3>基础表单效果</h3>
        <SimpleForm
          :config="formConfig"
          :initial-data="formInitialData"
          @submit="handleFormSubmit"
          @change="handleFormChange"
        />
      </div>
    </el-card>

    <!-- 高级表单示例 -->
    <el-card class="demo-card" shadow="never">
      <template #header>
        <h2>SimpleForm - 高级功能演示</h2>
      </template>

      <div class="demo-section">
        <h3>统一事件处理 + 双模式上传 + 图片预览</h3>
        <pre class="code-block">{{ advancedFormUsageCode }}</pre>

        <h3>高级表单效果</h3>
        <SimpleForm
          :config="advancedFormConfig"
          @submit="handleAdvancedFormSubmit"
          @change="handleAdvancedFormChange"
          @field-change="handleFieldChange"
        />

        <div class="form-data-display">
          <h4>实时表单数据：</h4>
          <pre class="data-block">{{ JSON.stringify(currentFormData, null, 2) }}</pre>
        </div>
      </div>
    </el-card>

    <!-- 功能特性说明 -->
    <el-card class="demo-card" shadow="never">
      <template #header>
        <h2>🚀 最新功能特性</h2>
      </template>

      <div class="demo-section">
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">🎯</div>
            <h4>统一事件处理</h4>
            <p>所有上传事件通过 <code>onEvent</code> 统一处理，告别繁琐的单独回调配置</p>
          </div>

          <div class="feature-item">
            <div class="feature-icon">🔄</div>
            <h4>双模式上传</h4>
            <p>有 <code>action</code> = 服务器上传<br>无 <code>action</code> = 本地预览（base64）</p>
          </div>

          <div class="feature-item">
            <div class="feature-icon">🛡️</div>
            <h4>智能限制控制</h4>
            <p>达到 <code>limit</code> 时自动触发 <code>exceed</code> 事件，无需手动控制按钮显示</p>
          </div>

          <div class="feature-item">
            <div class="feature-icon">🖼️</div>
            <h4>高级图片预览</h4>
            <p>点击图片自动弹窗预览，支持缩放、旋转、鼠标滚轮操作</p>
          </div>

          <div class="feature-item">
            <div class="feature-icon">⚙️</div>
            <h4>配置集中管理</h4>
            <p>初始数据在 <code>formConfig.initialData</code> 中统一配置，便于维护</p>
          </div>

          <div class="feature-item">
            <div class="feature-icon">📱</div>
            <h4>响应式设计</h4>
            <p>完美适配移动端，支持触摸操作和手势缩放</p>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 配置对比 -->
    <el-card class="demo-card" shadow="never">
      <template #header>
        <h2>配置复杂度对比</h2>
      </template>
      
      <div class="demo-section">
        <div class="comparison">
          <div class="comparison-item">
            <h3>优化前（复杂配置）</h3>
            <pre class="code-block old">{{ oldConfigCode }}</pre>
          </div>
          <div class="comparison-item">
            <h3>优化后（超简化）</h3>
            <pre class="code-block new">{{ newConfigCode }}</pre>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import SimpleTable from '@/components/table/SimpleTable.vue'
import SimpleForm from '@/components/form/SimpleForm.vue'

// 分页状态
const currentPage = ref(1)
const pageSize = ref(10)

// 表格配置
const tableConfig = {
  columns: ['id', 'username', 'email', 'status', 'createTime'],
  actions: [
    { type: 'edit', label: '编辑', style: 'primary' },
    { type: 'delete', label: '删除', style: 'danger' }
  ],
  pagination: true,
  selection: true
}

// 表格数据
const tableData = ref([
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    status: '1',
    createTime: '2024-06-19 10:00:00'
  },
  {
    id: 2,
    username: 'user1',
    email: '<EMAIL>',
    status: '0',
    createTime: '2024-06-18 15:30:00'
  }
])

const tableLoading = ref(false)

// 表单配置
const formConfig = {
  fields: ['username', 'email', 'password', 'status', 'description'],
  actions: [
    { type: 'submit', label: '提交', style: 'primary' },
    { type: 'reset', label: '重置', style: 'default' }
  ]
}

// 表单初始数据
const formInitialData = {
  username: '',
  email: '',
  password: '',
  status: '1',
  description: ''
}

// 当前表单数据（用于实时显示）
const currentFormData = ref({})

// 统一事件处理函数
const handleUploadEvent = (eventType, prop, ...args) => {
  console.log(`[SimpleDemo] Upload event: ${eventType} for ${prop}`, args)

  switch (eventType) {
    case 'success':
      const [response, file] = args
      ElMessage.success(`${prop} 上传成功！`)
      break

    case 'error':
      const [error] = args
      ElMessage.error(`${prop} 上传失败，请重试`)
      break

    case 'remove':
      const [removeFile] = args
      ElMessage.info(`${prop} 文件已删除`)
      break

    case 'beforeUpload':
      const [beforeFile] = args
      // 检查文件类型
      const isImage = beforeFile.type.startsWith('image/')
      if (!isImage) {
        ElMessage.error('只能上传图片文件！')
        return false
      }

      // 检查文件大小（限制为2MB）
      const isLt2M = beforeFile.size / 1024 / 1024 < 2
      if (!isLt2M) {
        ElMessage.error('图片文件大小不能超过 2MB！')
        return false
      }

      ElMessage.info(`开始处理 ${prop}...`)
      return true

    case 'preview':
      // 预览功能由 SimpleForm 的弹窗处理
      break

    case 'progress':
      const [event, progressFile] = args
      console.log(`${prop} 上传进度:`, Math.round(event.percent), '%')
      break

    case 'change':
      const [changeFile, fileList] = args
      ElMessage.info(`${prop} 文件状态改变: ${changeFile.name}`)
      break

    case 'exceed':
      const [files, currentFileList] = args
      ElMessage.warning(`${prop} 上传数量已达到上限！`)
      break

    default:
      console.log(`未处理的事件类型: ${eventType}`)
  }
}

// 高级表单配置
const advancedFormConfig = {
  labelWidth: '120px',
  // 初始数据配置
  initialData: {
    username: '',
    email: '',
    avatar: [],
    photos: [],
    description: '',
    tags: [],
    status: '1'
  },
  fields: [
    {
      type: 'input',
      prop: 'username',
      label: '用户名',
      placeholder: '请输入用户名',
      rules: [
        { required: true, message: '请输入用户名', trigger: 'blur' }
      ]
    },
    {
      type: 'input',
      prop: 'email',
      label: '邮箱',
      placeholder: '请输入邮箱地址',
      rules: [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ]
    },
    {
      type: 'upload',
      prop: 'avatar',
      label: '头像上传（服务器）',
      accept: 'image/*',
      action: '/api/upload',
      limit: 1,
      listType: 'picture-card',
      onEvent: handleUploadEvent
    },
    {
      type: 'upload',
      prop: 'photos',
      label: '相册上传（本地预览）',
      accept: 'image/*',
      // 不设置 action，实现本地文件处理
      limit: 3,
      listType: 'picture-card',
      multiple: true,
      onEvent: handleUploadEvent
    },
    {
      type: 'checkbox',
      prop: 'tags',
      label: '标签选择',
      options: [
        { label: 'Vue.js', value: 'vue' },
        { label: 'React', value: 'react' },
        { label: 'Angular', value: 'angular' },
        { label: 'Node.js', value: 'nodejs' }
      ]
    },
    {
      type: 'radio',
      prop: 'status',
      label: '状态',
      options: [
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' }
      ]
    },
    {
      type: 'textarea',
      prop: 'description',
      label: '描述',
      placeholder: '请输入描述信息',
      rows: 4
    }
  ],
  actions: [
    { type: 'submit', label: '提交', style: 'primary' },
    { type: 'reset', label: '重置', style: 'default' }
  ]
}

// 表格操作处理
const handleTableAction = ({ type, row }) => {
  if (type === 'edit') {
    ElMessage.success(`编辑用户：${row.username}`)
  } else if (type === 'delete') {
    ElMessage.warning(`删除用户：${row.username}`)
  }
}

// 分页处理
const handlePageChange = (page) => {
  currentPage.value = page
  ElMessage.info(`切换到第 ${page} 页`)
}

// 页面大小变化处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  ElMessage.info(`每页显示 ${size} 条`)
}

// 表单提交处理
const handleFormSubmit = (data) => {
  ElMessage.success('表单提交成功')
  console.log('表单数据:', data)
}

// 表单变化处理
const handleFormChange = (data) => {
  console.log('表单数据变化:', data)
}

// 高级表单提交处理
const handleAdvancedFormSubmit = (data) => {
  ElMessage.success('高级表单提交成功')
  console.log('高级表单数据:', data)
  currentFormData.value = data
}

// 高级表单变化处理
const handleAdvancedFormChange = (data) => {
  console.log('高级表单数据变化:', data)
  currentFormData.value = data
}

// 字段变化处理
const handleFieldChange = (prop, value) => {
  console.log(`字段 ${prop} 变化:`, value)
  currentFormData.value = { ...currentFormData.value, [prop]: value }
}

// 使用示例代码
const tableUsageCode = `// 表格使用 - 只需配置JSON和回调
<SimpleTable 
  :data="tableData"
  :config="{
    columns: ['id', 'username', 'email', 'status', 'createTime'],
    actions: [
      { type: 'edit', label: '编辑', style: 'primary' },
      { type: 'delete', label: '删除', style: 'danger' }
    ],
    pagination: true,
    selection: true
  }"
  :total="100"
  :current-page="currentPage"
  :page-size="pageSize"
  @action="handleTableAction"
  @page-change="handlePageChange"
  @size-change="handleSizeChange"
/>`

const formUsageCode = `// 表单使用 - 只需配置JSON和回调
<SimpleForm
  :config="{
    fields: ['username', 'email', 'password', 'status', 'description'],
    actions: [
      { type: 'submit', label: '提交', style: 'primary' },
      { type: 'reset', label: '重置', style: 'default' }
    ]
  }"
  :initial-data="formData"
  @submit="handleFormSubmit"
  @change="handleFormChange"
/>`

const advancedFormUsageCode = `// 高级表单功能演示
// 1. 统一事件处理函数
const handleUploadEvent = (eventType, prop, ...args) => {
  switch (eventType) {
    case 'success':
      ElMessage.success(\`\${prop} 上传成功！\`)
      break
    case 'error':
      ElMessage.error(\`\${prop} 上传失败\`)
      break
    case 'exceed':
      ElMessage.warning(\`\${prop} 上传数量已达到上限！\`)
      break
    // ... 更多事件处理
  }
}

// 2. 高级表单配置
const advancedFormConfig = {
  labelWidth: '120px',
  initialData: {
    username: '',
    email: '',
    avatar: [],      // 服务器上传模式
    photos: [],      // 本地预览模式
    description: ''
  },
  fields: [
    {
      type: 'upload',
      prop: 'avatar',
      label: '头像上传（服务器）',
      accept: 'image/*',
      action: '/api/upload',    // 有 action = 服务器上传
      limit: 1,
      listType: 'picture-card',
      onEvent: handleUploadEvent  // 统一事件处理
    },
    {
      type: 'upload',
      prop: 'photos',
      label: '相册上传（本地预览）',
      accept: 'image/*',
      // 无 action = 本地文件处理，转为 base64
      limit: 3,
      listType: 'picture-card',
      multiple: true,
      onEvent: handleUploadEvent  // 统一事件处理
    }
    // ... 更多字段
  ]
}

// 3. 使用组件
<SimpleForm
  :config="advancedFormConfig"
  @submit="handleAdvancedFormSubmit"
  @change="handleAdvancedFormChange"
  @field-change="handleFieldChange"
/>

// 特性说明：
// ✅ 统一事件处理：所有上传事件通过 onEvent 统一处理
// ✅ 双模式上传：有/无 action 自动切换服务器/本地模式
// ✅ 智能限制：达到 limit 时自动触发 exceed 事件
// ✅ 图片预览：点击图片自动弹窗预览，支持缩放旋转
// ✅ 初始数据：在 formConfig.initialData 中统一配置`

const oldConfigCode = `// 优化前：需要配置大量细节
const tableConfig = {
  columns: [
    {
      prop: 'id',
      label: 'ID',
      width: 80
    },
    {
      prop: 'username',
      label: '用户名',
      width: 120
    },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      type: 'tag',
      tagMap: {
        '1': { type: 'success', text: '启用' },
        '0': { type: 'danger', text: '禁用' }
      }
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 160,
      type: 'datetime'
    }
  ],
  actions: [
    {
      type: 'edit',
      label: '编辑',
      style: 'primary',
      icon: 'Edit',
      permission: 'user:edit'
    }
  ],
  pagination: {
    show: true,
    currentPage: 1,
    pageSize: 10,
    total: 0,
    pageSizes: [10, 20, 50, 100]
  },
  selection: true,
  border: true,
  stripe: true
}`

const newConfigCode = `// 优化后：智能推断，极简配置
const tableConfig = {
  columns: ['id', 'username', 'status', 'createTime'],
  actions: [
    { type: 'edit', label: '编辑', style: 'primary' }
  ],
  pagination: true,
  selection: true
}

// 自动推断：
// - id -> 文本列，80px宽度
// - username -> 文本列，自动标题"用户名"
// - status -> 标签列，自动标签映射
// - createTime -> 日期时间列，160px宽度`
</script>

<style scoped>
.simple-demo-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 10px 0;
}

.page-description {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.demo-card {
  margin-bottom: 30px;
}

.demo-card h2 {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.demo-section h3 {
  font-size: 16px;
  font-weight: 500;
  color: #409eff;
  margin: 20px 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.code-block {
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #606266;
  overflow-x: auto;
  white-space: pre-wrap;
}

.comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.comparison-item h3 {
  margin-top: 0;
}

.code-block.old {
  background: #fef0f0;
  border-color: #fbc4c4;
  color: #f56c6c;
}

.code-block.new {
  background: #f0f9ff;
  border-color: #b3d8ff;
  color: #409eff;
}

.form-data-display {
  margin-top: 30px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.form-data-display h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.data-block {
  background: #ffffff;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 16px;
  margin: 0;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #606266;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.feature-item {
  padding: 20px;
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
}

.feature-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 15px;
}

.feature-item h4 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 10px 0;
}

.feature-item p {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  margin: 0;
}

.feature-item code {
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  color: #e6a23c;
  font-family: 'Monaco', 'Consolas', monospace;
}

@media (max-width: 768px) {
  .comparison {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .feature-item {
    padding: 15px;
  }

  .feature-icon {
    font-size: 28px;
    margin-bottom: 10px;
  }

  .simple-demo-page {
    padding: 15px;
  }

  .form-data-display {
    padding: 15px;
  }

  .data-block {
    font-size: 11px;
    padding: 12px;
  }
}
</style>
