<template>
  <div class="module-control">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>模块显示控制</span>
          <el-button type="primary" @click="refreshMenu">刷新菜单</el-button>
        </div>
      </template>
      
      <div class="module-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="总模块数" :value="stats.total" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="显示模块" :value="stats.visible" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="隐藏模块" :value="stats.hidden" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="显示率" :value="stats.visibilityRate" suffix="%" />
          </el-col>
        </el-row>
      </div>
      
      <el-divider />
      
      <div class="module-list">
        <h3>模块控制面板</h3>
        <el-table :data="moduleList" style="width: 100%">
          <el-table-column prop="name" label="模块名称" width="120" />
          <el-table-column prop="title" label="显示名称" width="150" />
          <el-table-column prop="icon" label="图标" width="80">
            <template #default="scope">
              <el-icon><component :is="scope.row.icon" /></el-icon>
            </template>
          </el-table-column>
          <el-table-column prop="order" label="排序" width="80" />
          <el-table-column prop="permission" label="权限" width="200">
            <template #default="scope">
              <el-tag v-if="Array.isArray(scope.row.permission)" type="warning" size="small">
                多权限 ({{ scope.row.permission.length }})
              </el-tag>
              <el-tag v-else type="info" size="small">
                {{ scope.row.permission }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" />
          <el-table-column label="显示状态" width="100">
            <template #default="scope">
              <el-switch
                v-model="scope.row.visible"
                @change="handleVisibilityChange(scope.row.name, scope.row.visible)"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <el-divider />
      
      <div class="batch-operations">
        <h3>批量操作</h3>
        <el-space>
          <el-button @click="showAllModules">显示所有模块</el-button>
          <el-button @click="hideAllModules">隐藏所有模块</el-button>
          <el-button @click="resetToDefault">重置为默认</el-button>
          <el-button type="danger" @click="hideNonEssential">只显示核心模块</el-button>
        </el-space>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  moduleConfig, 
  getModuleStats, 
  setModuleVisibility, 
  setMultipleModuleVisibility,
  resetAllModulesVisible 
} from '@/config/modules.js'

// 模块列表
const moduleList = ref([])

// 统计信息
const stats = computed(() => getModuleStats())

// 初始化模块列表
const initModuleList = () => {
  moduleList.value = Object.entries(moduleConfig).map(([name, config]) => ({
    name,
    ...config
  })).sort((a, b) => a.order - b.order)
}

// 处理显示状态变化
const handleVisibilityChange = (moduleName, visible) => {
  const success = setModuleVisibility(moduleName, visible)
  if (success) {
    ElMessage.success(`${visible ? '显示' : '隐藏'}模块: ${moduleConfig[moduleName].title}`)
  } else {
    ElMessage.error('操作失败')
  }
}

// 刷新菜单
const refreshMenu = () => {
  // 这里可以触发菜单重新加载
  window.location.reload()
}

// 显示所有模块
const showAllModules = () => {
  const settings = {}
  Object.keys(moduleConfig).forEach(name => {
    settings[name] = true
  })
  setMultipleModuleVisibility(settings)
  initModuleList()
  ElMessage.success('已显示所有模块')
}

// 隐藏所有模块
const hideAllModules = () => {
  const settings = {}
  Object.keys(moduleConfig).forEach(name => {
    settings[name] = false
  })
  setMultipleModuleVisibility(settings)
  initModuleList()
  ElMessage.warning('已隐藏所有模块')
}

// 重置为默认
const resetToDefault = () => {
  resetAllModulesVisible()
  initModuleList()
  ElMessage.success('已重置为默认设置')
}

// 只显示核心模块
const hideNonEssential = () => {
  const essentialModules = ['dashboard', 'user', 'system']
  const settings = {}
  Object.keys(moduleConfig).forEach(name => {
    settings[name] = essentialModules.includes(name)
  })
  setMultipleModuleVisibility(settings)
  initModuleList()
  ElMessage.info('已设置为只显示核心模块')
}

onMounted(() => {
  initModuleList()
})
</script>

<style scoped>
.module-control {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.module-stats {
  margin-bottom: 20px;
}

.module-list {
  margin: 20px 0;
}

.batch-operations {
  margin-top: 20px;
}

.el-statistic {
  text-align: center;
}
</style>
