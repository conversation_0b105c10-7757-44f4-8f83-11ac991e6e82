import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Refresh, Check } from '@element-plus/icons-vue'
import { configAPI } from '@/api'

/**
 * 配置创建页面组合式函数
 * @returns {Object} 返回模板需要的所有数据和方法
 */
export function useConfigCreate() {
  const router = useRouter()
  const route = useRoute()

  // 表单引用
  const formRef = ref()

  // 页面状态
  const submitting = ref(false)
  const pageTitle = ref('新增配置')

  // 表单数据
  const formData = reactive({})

  // 表单配置
  const formConfig = reactive({
    labelWidth: '120px',
    // 初始数据配置
    initialData: {
      config_name: '',
      config_value: '',
      desc: ''
    },
    fields: [
      {
        type: 'input',
        prop: 'config_name',
        label: '配置名称',
        placeholder: '请输入配置名称（如：site_title）',
        rules: [
          { required: true, message: '请输入配置名称', trigger: 'blur' },
          { min: 2, max: 50, message: '配置名称长度在 2 到 50 个字符', trigger: 'blur' },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '配置名称只能包含字母、数字和下划线，且以字母开头', trigger: 'blur' }
        ]
      },
      {
        type: 'textarea',
        prop: 'config_value',
        label: '配置值',
        placeholder: '请输入配置值',
        rows: 4,
        rules: [
          { required: true, message: '请输入配置值', trigger: 'blur' }
        ]
      },
      {
        type: 'textarea',
        prop: 'desc',
        label: '配置描述',
        placeholder: '请输入配置描述',
        rows: 3,
        rules: [
          { required: true, message: '请输入配置描述', trigger: 'blur' },
          { max: 200, message: '配置描述不能超过200个字符', trigger: 'blur' }
        ]
      }
    ],
    actions: [
      { type: 'submit', label: '保存', style: 'primary' },
      { type: 'reset', label: '重置', style: 'default' }
    ]
  })

  // 初始化表单数据
  Object.assign(formData, formConfig.initialData)

  // 返回上一页
  const handleBack = () => {
    router.back()
  }

  // 重置表单
  const handleReset = () => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
    // 从配置中重置数据
    Object.assign(formData, formConfig.initialData)
    ElMessage.info('表单已重置')
  }

  // 提交表单
  const handleSubmit = async () => {
    try {
      // 表单验证
      await formRef.value?.validate()

      submitting.value = true

      // 准备提交数据
      const configData = { ...formData }

      console.log('创建配置数据:', configData)

      // 调用API创建配置
      await configAPI.createConfig(configData)

      ElMessage.success('配置创建成功！')

      // 跳转回配置列表
      setTimeout(() => {
        router.push('/admin/config-list')
      }, 1000)

    } catch (error) {
      if (error !== 'cancel') {
        console.error('创建配置失败:', error)
        ElMessage.error('创建配置失败，请检查表单信息')
      }
    } finally {
      submitting.value = false
    }
  }

  // 表单提交处理（由SimpleForm触发）
  const handleFormSubmit = (data) => {
    Object.assign(formData, data)
    handleSubmit()
  }

  // 表单重置处理（由SimpleForm触发）
  const handleFormReset = () => {
    handleReset()
  }

  // 字段变化处理
  const handleFieldChange = (prop, value) => {
    console.log(`[ConfigCreate] Field change: ${prop}`, value)

    // 更新表单数据
    if (typeof prop === 'object') {
      // 如果传入的是对象，批量更新
      Object.assign(formData, prop)
    } else {
      // 单个字段更新
      formData[prop] = value
    }
  }

  // 返回模板需要的所有数据和方法
  return {
    // 图标组件
    ArrowLeft,
    Refresh,
    Check,
    // 表单引用
    formRef,
    // 响应式数据
    submitting,
    pageTitle,
    formData,
    formConfig,
    // 方法
    handleBack,
    handleReset,
    handleSubmit,
    handleFormSubmit,
    handleFormReset,
    handleFieldChange
  }
}
