<template>
  <div class="config-list-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">配置管理</h1>
      <div class="header-actions">
        <el-button 
          v-permission="'config:create'"
          type="primary" 
          @click="handleAdd"
        >
          <el-icon><Plus /></el-icon>
          新增配置
        </el-button>
      </div>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchFormData" inline>
        <el-form-item label="配置名称">
          <el-input 
            v-model="searchFormData.config_name" 
            placeholder="请输入配置名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="配置描述">
          <el-input 
            v-model="searchFormData.desc" 
            placeholder="请输入配置描述"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <SimpleTable 
        :data="tableData"
        :config="tableConfig"
        :loading="loading"
        :total="total"
        :current-page="currentPage"
        :page-size="pageSize"
        @action="handleTableAction"
        @selection-change="handleSelectionChange"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </el-card>
  </div>
</template>

<script setup>
import { useConfigList } from './config_list.js'
import SimpleTable from '@/components/table/SimpleTable.vue'

// 使用配置列表组合式函数
const {
  // 图标组件
  Plus,
  Search,
  Refresh,
  // 响应式数据
  searchFormData,
  tableData,
  tableConfig,
  loading,
  total,
  currentPage,
  pageSize,
  // 方法
  handleAdd,
  handleSearch,
  handleReset,
  handleTableAction,
  handleSelectionChange,
  handlePageChange,
  handleSizeChange
} = useConfigList()
</script>

<style scoped src="./config_list.css"></style>
