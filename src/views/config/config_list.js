import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { configAPI } from '@/api'

/**
 * 配置列表页面组合式函数
 * @returns {Object} 返回模板需要的所有数据和方法
 */
export function useConfigList() {
  const router = useRouter()

  // 分页状态
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const loading = ref(false)

  // 搜索表单数据
  const searchFormData = reactive({
    config_name: '',
    desc: ''
  })

  // 表格配置
  const tableConfig = {
    columns: [
      { prop: 'id', label: 'ID', width: 80 },
      { prop: 'config_name', label: '配置名称', width: 200 },
      { prop: 'config_value', label: '配置值', width: 300 },
      { prop: 'desc', label: '配置描述', width: 200 },
      { prop: 'create_time', label: '创建时间', type: 'datetime', width: 160 },
      { prop: 'update_time', label: '更新时间', type: 'datetime', width: 160 }
    ],
    actions: [
      { type: 'view', label: '查看', style: 'text' },
      { type: 'edit', label: '编辑', style: 'primary' },
      { type: 'delete', label: '删除', style: 'danger' }
    ],
    pagination: true,
    selection: true
  }

  // 表格数据
  const tableData = ref([])

  // 模拟配置数据
  const mockConfigs = [
    {
      id: 1,
      config_name: 'site_title',
      config_value: '疗愈管理系统',
      desc: '网站标题配置',
      create_time: '2024-01-01 10:00:00',
      update_time: '2024-06-19 10:30:00'
    },
    {
      id: 2,
      config_name: 'site_logo',
      config_value: '/images/logo.png',
      desc: '网站Logo配置',
      create_time: '2024-01-01 10:00:00',
      update_time: '2024-06-19 10:30:00'
    },
    {
      id: 3,
      config_name: 'max_upload_size',
      config_value: '10MB',
      desc: '最大上传文件大小',
      create_time: '2024-01-01 10:00:00',
      update_time: '2024-06-19 10:30:00'
    },
    {
      id: 4,
      config_name: 'session_timeout',
      config_value: '3600',
      desc: '会话超时时间（秒）',
      create_time: '2024-01-01 10:00:00',
      update_time: '2024-06-19 10:30:00'
    },
    {
      id: 5,
      config_name: 'email_smtp_host',
      config_value: 'smtp.qq.com',
      desc: '邮件SMTP服务器',
      create_time: '2024-01-01 10:00:00',
      update_time: '2024-06-19 10:30:00'
    },
    {
      id: 6,
      config_name: 'email_smtp_port',
      config_value: '587',
      desc: '邮件SMTP端口',
      create_time: '2024-01-01 10:00:00',
      update_time: '2024-06-19 10:30:00'
    },
    {
      id: 7,
      config_name: 'backup_frequency',
      config_value: 'daily',
      desc: '数据备份频率',
      create_time: '2024-01-01 10:00:00',
      update_time: '2024-06-19 10:30:00'
    },
    {
      id: 8,
      config_name: 'maintenance_mode',
      config_value: 'false',
      desc: '维护模式开关',
      create_time: '2024-01-01 10:00:00',
      update_time: '2024-06-19 10:30:00'
    }
  ]

  // 获取配置列表
  const fetchConfigList = async () => {
    loading.value = true
    try {
      const params = {
        page: currentPage.value,
        limit: pageSize.value,
        ...searchFormData
      }

      const response = await configAPI.getConfigList(params)

      if (response && response.data) {
        tableData.value = response.data.list || []
        total.value = response.data.total || 0
      } else {
        tableData.value = response.list || response || []
        total.value = response.total || tableData.value.length
      }

    } catch (error) {
      console.error('获取配置列表失败:', error)
      ElMessage.error('获取配置列表失败，使用模拟数据')

      // 如果API失败，使用模拟数据作为备用
      let filteredData = [...mockConfigs]

      if (searchFormData.config_name) {
        filteredData = filteredData.filter(config =>
          config.config_name.includes(searchFormData.config_name)
        )
      }

      if (searchFormData.desc) {
        filteredData = filteredData.filter(config =>
          config.desc.includes(searchFormData.desc)
        )
      }

      // 模拟分页
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value

      tableData.value = filteredData.slice(start, end)
      total.value = filteredData.length
    } finally {
      loading.value = false
    }
  }

  // 搜索配置
  const handleSearch = () => {
    currentPage.value = 1
    fetchConfigList()
    ElMessage.success('搜索完成')
  }

  // 重置搜索
  const handleReset = () => {
    Object.assign(searchFormData, {
      config_name: '',
      desc: ''
    })
    currentPage.value = 1
    fetchConfigList()
    ElMessage.info('已重置搜索条件')
  }

  // 新增配置
  const handleAdd = () => {
    router.push('/admin/config-list/config-create')
  }

  // 表格操作处理
  const handleTableAction = ({ type, row }) => {
    switch (type) {
      case 'view':
        router.push(`/admin/config-list/config-edit/${row.id}?mode=view`)
        break
      case 'edit':
        router.push(`/admin/config-list/config-edit/${row.id}`)
        break
      case 'delete':
        handleDelete(row)
        break
      default:
        ElMessage.warning(`未知操作：${type}`)
    }
  }

  // 删除配置
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除配置 "${row.config_name}" 吗？`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      // 模拟删除API
      await new Promise(resolve => setTimeout(resolve, 500))
      
      ElMessage.success(`配置 "${row.config_name}" 删除成功`)
      fetchConfigList()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 选择变化
  const handleSelectionChange = (selection) => {
    console.log('选中的配置：', selection)
    if (selection.length > 0) {
      ElMessage.info(`已选中 ${selection.length} 个配置`)
    }
  }

  // 分页改变
  const handlePageChange = (page) => {
    currentPage.value = page
    fetchConfigList()
  }

  // 页面大小改变
  const handleSizeChange = (size) => {
    pageSize.value = size
    currentPage.value = 1
    fetchConfigList()
  }

  // 页面加载时获取数据
  onMounted(() => {
    fetchConfigList()
  })

  // 返回模板需要的所有数据和方法
  return {
    // 图标组件
    Plus,
    Search,
    Refresh,
    // 响应式数据
    searchFormData,
    tableData,
    tableConfig,
    loading,
    total,
    currentPage,
    pageSize,
    // 方法
    handleAdd,
    handleSearch,
    handleReset,
    handleTableAction,
    handleSelectionChange,
    handlePageChange,
    handleSizeChange
  }
}
