import { http } from '../request'

/**
 * 配置管理相关API
 */

// 获取配置列表
export const getConfigList = (params = {}) => {
  return http.get('/admin/config/list', {
    page: 1,
    limit: 10,
    ...params
  })
}

// 获取配置详情
export const getConfigDetail = (id) => {
  return http.get(`/admin/config/detail/${id}`)
}

// 根据配置名称获取配置
export const getConfigByName = (configName) => {
  return http.get(`/admin/config/name/${configName}`)
}

// 创建配置
export const createConfig = (data) => {
  return http.post('/admin/config/create', data)
}

// 更新配置
export const updateConfig = (id, data) => {
  return http.put(`/admin/config/update/${id}`, data)
}

// 删除配置
export const deleteConfig = (id) => {
  return http.delete(`/admin/config/delete/${id}`)
}

// 批量删除配置
export const batchDeleteConfigs = (ids) => {
  return http.post('/admin/config/batch-delete', { ids })
}

// 批量更新配置
export const batchUpdateConfigs = (configs) => {
  return http.post('/admin/config/batch-update', { configs })
}

// 获取所有配置（键值对格式）
export const getAllConfigs = () => {
  return http.get('/admin/config/all')
}

// 重置配置到默认值
export const resetConfig = (id) => {
  return http.post(`/admin/config/reset/${id}`)
}

// 导入配置
export const importConfigs = (file) => {
  const formData = new FormData()
  formData.append('file', file)
  return http.upload('/admin/config/import', formData)
}

// 导出配置
export const exportConfigs = (params = {}) => {
  return http.get('/admin/config/export', params, {
    responseType: 'blob'
  })
}

// 验证配置值
export const validateConfigValue = (configName, configValue) => {
  return http.post('/admin/config/validate', {
    config_name: configName,
    config_value: configValue
  })
}

// 获取配置分组
export const getConfigGroups = () => {
  return http.get('/admin/config/groups')
}

// 搜索配置
export const searchConfigs = (keyword) => {
  return http.get('/admin/config/search', { keyword })
}

export default {
  getConfigList,
  getConfigDetail,
  getConfigByName,
  createConfig,
  updateConfig,
  deleteConfig,
  batchDeleteConfigs,
  batchUpdateConfigs,
  getAllConfigs,
  resetConfig,
  importConfigs,
  exportConfigs,
  validateConfigValue,
  getConfigGroups,
  searchConfigs
}
