/**
 * 用户管理相关API
 */

import { request, cachedGet } from '../request'

/**
 * 获取用户列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页数量
 * @param {string} params.username 用户名
 * @param {string} params.email 邮箱
 * @param {string} params.status 状态
 * @param {string} params.role 角色
 * @returns {Promise}
 */
export function getUserList(params) {
  return request.get('/users', params)
}

/**
 * 获取用户详情
 * @param {number|string} id 用户ID
 * @returns {Promise}
 */
export function getUserDetail(id) {
  return cachedGet(`/users/${id}`, {}, {}, 60000) // 缓存1分钟
}

/**
 * 创建用户
 * @param {Object} data 用户数据
 * @param {string} data.username 用户名
 * @param {string} data.email 邮箱
 * @param {string} data.password 密码
 * @param {string} data.realName 真实姓名
 * @param {string} data.phone 手机号
 * @param {string} data.role 角色
 * @param {string} data.status 状态
 * @returns {Promise}
 */
export function createUser(data) {
  return request.post('/users', data)
}

/**
 * 更新用户信息
 * @param {number|string} id 用户ID
 * @param {Object} data 用户数据
 * @returns {Promise}
 */
export function updateUser(id, data) {
  return request.put(`/users/${id}`, data)
}

/**
 * 删除用户
 * @param {number|string} id 用户ID
 * @returns {Promise}
 */
export function deleteUser(id) {
  return request.delete(`/users/${id}`)
}

/**
 * 批量删除用户
 * @param {Array} ids 用户ID数组
 * @returns {Promise}
 */
export function batchDeleteUsers(ids) {
  return request.delete('/users/batch', { ids })
}

/**
 * 启用/禁用用户
 * @param {number|string} id 用户ID
 * @param {string} status 状态 ('1'=启用, '0'=禁用)
 * @returns {Promise}
 */
export function toggleUserStatus(id, status) {
  return request.patch(`/users/${id}/status`, { status })
}

/**
 * 重置用户密码
 * @param {number|string} id 用户ID
 * @param {string} newPassword 新密码
 * @returns {Promise}
 */
export function resetUserPassword(id, newPassword) {
  return request.patch(`/users/${id}/password`, { password: newPassword })
}

/**
 * 更新用户头像
 * @param {number|string} id 用户ID
 * @param {File} file 头像文件
 * @returns {Promise}
 */
export function updateUserAvatar(id, file) {
  const formData = new FormData()
  formData.append('avatar', file)
  return request.upload(`/users/${id}/avatar`, formData)
}

/**
 * 获取用户权限
 * @param {number|string} id 用户ID
 * @returns {Promise}
 */
export function getUserPermissions(id) {
  return cachedGet(`/users/${id}/permissions`, {}, {}, 300000) // 缓存5分钟
}

/**
 * 设置用户权限
 * @param {number|string} id 用户ID
 * @param {Array} permissions 权限数组
 * @returns {Promise}
 */
export function setUserPermissions(id, permissions) {
  return request.put(`/users/${id}/permissions`, { permissions })
}

/**
 * 获取用户角色列表
 * @param {number|string} id 用户ID
 * @returns {Promise}
 */
export function getUserRoles(id) {
  return request.get(`/users/${id}/roles`)
}

/**
 * 设置用户角色
 * @param {number|string} id 用户ID
 * @param {Array} roles 角色数组
 * @returns {Promise}
 */
export function setUserRoles(id, roles) {
  return request.put(`/users/${id}/roles`, { roles })
}

/**
 * 获取用户统计信息
 * @returns {Promise}
 */
export function getUserStats() {
  return cachedGet('/users/stats', {}, {}, 60000) // 缓存1分钟
}

/**
 * 导出用户数据
 * @param {Object} params 导出参数
 * @param {string} params.format 导出格式 ('excel', 'csv')
 * @param {Array} params.fields 导出字段
 * @param {Object} params.filters 过滤条件
 * @returns {Promise}
 */
export function exportUsers(params) {
  return request.download('/users/export', params)
}

/**
 * 导入用户数据
 * @param {File} file 导入文件
 * @param {Object} options 导入选项
 * @returns {Promise}
 */
export function importUsers(file, options = {}) {
  const formData = new FormData()
  formData.append('file', file)
  Object.keys(options).forEach(key => {
    formData.append(key, options[key])
  })
  return request.upload('/users/import', formData)
}

/**
 * 获取导入模板
 * @param {string} format 模板格式 ('excel', 'csv')
 * @returns {Promise}
 */
export function getImportTemplate(format = 'excel') {
  return request.download('/users/import/template', { format })
}

/**
 * 验证用户名是否可用
 * @param {string} username 用户名
 * @param {number|string} excludeId 排除的用户ID（用于编辑时）
 * @returns {Promise}
 */
export function checkUsername(username, excludeId = null) {
  const params = { username }
  if (excludeId) {
    params.excludeId = excludeId
  }
  return request.get('/users/check/username', params)
}

/**
 * 验证邮箱是否可用
 * @param {string} email 邮箱
 * @param {number|string} excludeId 排除的用户ID（用于编辑时）
 * @returns {Promise}
 */
export function checkEmail(email, excludeId = null) {
  const params = { email }
  if (excludeId) {
    params.excludeId = excludeId
  }
  return request.get('/users/check/email', params)
}

/**
 * 获取在线用户列表
 * @returns {Promise}
 */
export function getOnlineUsers() {
  return request.get('/users/online')
}

/**
 * 强制用户下线
 * @param {number|string} id 用户ID
 * @returns {Promise}
 */
export function forceLogout(id) {
  return request.post(`/users/${id}/force-logout`)
}

/**
 * 获取用户操作日志
 * @param {number|string} id 用户ID
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getUserLogs(id, params) {
  return request.get(`/users/${id}/logs`, params)
}

/**
 * 用户分组相关API
 */
export const userGroup = {
  /**
   * 获取用户分组列表
   * @returns {Promise}
   */
  getList() {
    return cachedGet('/user-groups', {}, {}, 300000) // 缓存5分钟
  },

  /**
   * 创建用户分组
   * @param {Object} data 分组数据
   * @returns {Promise}
   */
  create(data) {
    return request.post('/user-groups', data)
  },

  /**
   * 更新用户分组
   * @param {number|string} id 分组ID
   * @param {Object} data 分组数据
   * @returns {Promise}
   */
  update(id, data) {
    return request.put(`/user-groups/${id}`, data)
  },

  /**
   * 删除用户分组
   * @param {number|string} id 分组ID
   * @returns {Promise}
   */
  delete(id) {
    return request.delete(`/user-groups/${id}`)
  },

  /**
   * 获取分组用户
   * @param {number|string} groupId 分组ID
   * @returns {Promise}
   */
  getUsers(groupId) {
    return request.get(`/user-groups/${groupId}/users`)
  },

  /**
   * 添加用户到分组
   * @param {number|string} groupId 分组ID
   * @param {Array} userIds 用户ID数组
   * @returns {Promise}
   */
  addUsers(groupId, userIds) {
    return request.post(`/user-groups/${groupId}/users`, { userIds })
  },

  /**
   * 从分组移除用户
   * @param {number|string} groupId 分组ID
   * @param {Array} userIds 用户ID数组
   * @returns {Promise}
   */
  removeUsers(groupId, userIds) {
    return request.delete(`/user-groups/${groupId}/users`, { userIds })
  }
}
