/**
 * 控制台相关API
 */

import { request, cachedGet } from '../request'

/**
 * 获取控制台统计数据
 * @returns {Promise}
 */
export function getDashboardStats() {
  return cachedGet('/dashboard/stats', {}, {}, 60000) // 缓存1分钟
}

/**
 * 获取实时数据
 * @returns {Promise}
 */
export function getRealTimeData() {
  return request.get('/dashboard/realtime')
}

/**
 * 获取图表数据
 * @param {string} type 图表类型 ('visits', 'users', 'orders', 'revenue')
 * @param {string} period 时间周期 ('day', 'week', 'month', 'year')
 * @returns {Promise}
 */
export function getChartData(type, period = 'week') {
  return cachedGet('/dashboard/charts', { type, period }, {}, 300000) // 缓存5分钟
}

/**
 * 获取系统监控数据
 * @returns {Promise}
 */
export function getSystemMonitor() {
  return request.get('/dashboard/system-monitor')
}

/**
 * 获取最新动态
 * @param {number} limit 数量限制
 * @returns {Promise}
 */
export function getRecentActivities(limit = 10) {
  return cachedGet('/dashboard/activities', { limit }, {}, 60000) // 缓存1分钟
}

/**
 * 获取快捷操作配置
 * @returns {Promise}
 */
export function getQuickActions() {
  return cachedGet('/dashboard/quick-actions', {}, {}, 600000) // 缓存10分钟
}

/**
 * 保存快捷操作配置
 * @param {Array} actions 快捷操作数组
 * @returns {Promise}
 */
export function saveQuickActions(actions) {
  return request.put('/dashboard/quick-actions', { actions })
}

/**
 * 获取用户自定义面板配置
 * @returns {Promise}
 */
export function getDashboardConfig() {
  return request.get('/dashboard/config')
}

/**
 * 保存用户自定义面板配置
 * @param {Object} config 面板配置
 * @returns {Promise}
 */
export function saveDashboardConfig(config) {
  return request.put('/dashboard/config', config)
}

/**
 * 获取数据统计报告
 * @param {Object} params 查询参数
 * @param {string} params.startDate 开始日期
 * @param {string} params.endDate 结束日期
 * @param {string} params.type 报告类型
 * @returns {Promise}
 */
export function getStatsReport(params) {
  return request.get('/dashboard/reports/stats', params)
}

/**
 * 导出统计报告
 * @param {Object} params 导出参数
 * @param {string} params.format 导出格式 ('excel', 'pdf', 'csv')
 * @param {string} params.startDate 开始日期
 * @param {string} params.endDate 结束日期
 * @returns {Promise}
 */
export function exportStatsReport(params) {
  return request.download('/dashboard/reports/export', params)
}

/**
 * 获取热门内容
 * @param {string} type 内容类型
 * @param {number} limit 数量限制
 * @returns {Promise}
 */
export function getPopularContent(type = 'all', limit = 10) {
  return cachedGet('/dashboard/popular', { type, limit }, {}, 300000) // 缓存5分钟
}

/**
 * 获取系统公告
 * @returns {Promise}
 */
export function getSystemNotices() {
  return cachedGet('/dashboard/notices', {}, {}, 300000) // 缓存5分钟
}

/**
 * 标记公告为已读
 * @param {number|string} noticeId 公告ID
 * @returns {Promise}
 */
export function markNoticeAsRead(noticeId) {
  return request.post(`/dashboard/notices/${noticeId}/read`)
}

/**
 * 获取待办事项
 * @returns {Promise}
 */
export function getTodoList() {
  return request.get('/dashboard/todos')
}

/**
 * 创建待办事项
 * @param {Object} data 待办数据
 * @returns {Promise}
 */
export function createTodo(data) {
  return request.post('/dashboard/todos', data)
}

/**
 * 更新待办事项
 * @param {number|string} id 待办ID
 * @param {Object} data 待办数据
 * @returns {Promise}
 */
export function updateTodo(id, data) {
  return request.put(`/dashboard/todos/${id}`, data)
}

/**
 * 删除待办事项
 * @param {number|string} id 待办ID
 * @returns {Promise}
 */
export function deleteTodo(id) {
  return request.delete(`/dashboard/todos/${id}`)
}

/**
 * 完成待办事项
 * @param {number|string} id 待办ID
 * @returns {Promise}
 */
export function completeTodo(id) {
  return request.patch(`/dashboard/todos/${id}/complete`)
}
