import { http } from '../request'

/**
 * 后台用户管理相关API
 */

// 获取后台用户列表
export const getAdminUserList = (params = {}) => {
  return http.get('/admin/user/list', {
    page: 1,
    limit: 10,
    ...params
  })
}

// 获取后台用户详情
export const getAdminUserDetail = (id) => {
  return http.get(`/admin/user/detail/${id}`)
}

// 创建后台用户
export const createAdminUser = (data) => {
  return http.post('/admin/user/create', data)
}

// 更新后台用户
export const updateAdminUser = (id, data) => {
  return http.put(`/admin/user/update/${id}`, data)
}

// 删除后台用户
export const deleteAdminUser = (id) => {
  return http.delete(`/admin/user/delete/${id}`)
}

// 批量删除后台用户
export const batchDeleteAdminUsers = (ids) => {
  return http.post('/admin/user/batch-delete', { ids })
}

// 重置用户密码
export const resetAdminUserPassword = (id, newPassword) => {
  return http.post(`/admin/user/reset-password/${id}`, { 
    new_password: newPassword 
  })
}

// 批量重置密码
export const batchResetAdminUserPassword = (ids, newPassword) => {
  return http.post('/admin/user/batch-reset-password', { 
    ids, 
    new_password: newPassword 
  })
}

// 更新用户状态（启用/禁用）
export const updateAdminUserStatus = (id, status) => {
  return http.patch(`/admin/user/status/${id}`, { status })
}

// 批量更新用户状态
export const batchUpdateAdminUserStatus = (ids, status) => {
  return http.post('/admin/user/batch-status', { ids, status })
}

// 更新用户角色
export const updateAdminUserRole = (id, role) => {
  return http.patch(`/admin/user/role/${id}`, { role })
}

// 批量更新用户角色
export const batchUpdateAdminUserRole = (ids, role) => {
  return http.post('/admin/user/batch-role', { ids, role })
}

// 获取用户权限列表
export const getAdminUserPermissions = (id) => {
  return http.get(`/admin/user/permissions/${id}`)
}

// 更新用户权限
export const updateAdminUserPermissions = (id, permissions) => {
  return http.put(`/admin/user/permissions/${id}`, { permissions })
}

// 获取用户登录日志
export const getAdminUserLoginLogs = (id, params = {}) => {
  return http.get(`/admin/user/login-logs/${id}`, {
    page: 1,
    limit: 10,
    ...params
  })
}

// 获取用户操作日志
export const getAdminUserOperationLogs = (id, params = {}) => {
  return http.get(`/admin/user/operation-logs/${id}`, {
    page: 1,
    limit: 10,
    ...params
  })
}

// 强制用户下线
export const forceAdminUserLogout = (id) => {
  return http.post(`/admin/user/force-logout/${id}`)
}

// 批量强制用户下线
export const batchForceAdminUserLogout = (ids) => {
  return http.post('/admin/user/batch-force-logout', { ids })
}

// 获取在线用户列表
export const getOnlineAdminUsers = () => {
  return http.get('/admin/user/online')
}

// 获取用户统计数据
export const getAdminUserStats = (params = {}) => {
  return http.get('/admin/user/stats', params)
}

// 导出用户数据
export const exportAdminUsers = (params = {}) => {
  return http.get('/admin/user/export', params, {
    responseType: 'blob'
  })
}

// 导入用户数据
export const importAdminUsers = (file) => {
  const formData = new FormData()
  formData.append('file', file)
  return http.upload('/admin/user/import', formData)
}

// 检查用户名是否可用
export const checkAdminUsername = (username) => {
  return http.get('/admin/user/check-username', { username })
}

// 检查邮箱是否可用
export const checkAdminEmail = (email) => {
  return http.get('/admin/user/check-email', { email })
}

// 发送密码重置邮件
export const sendPasswordResetEmail = (id) => {
  return http.post(`/admin/user/send-reset-email/${id}`)
}

// 用户搜索建议
export const getAdminUserSuggestions = (keyword) => {
  return http.get('/admin/user/suggestions', { keyword })
}

export default {
  getAdminUserList,
  getAdminUserDetail,
  createAdminUser,
  updateAdminUser,
  deleteAdminUser,
  batchDeleteAdminUsers,
  resetAdminUserPassword,
  batchResetAdminUserPassword,
  updateAdminUserStatus,
  batchUpdateAdminUserStatus,
  updateAdminUserRole,
  batchUpdateAdminUserRole,
  getAdminUserPermissions,
  updateAdminUserPermissions,
  getAdminUserLoginLogs,
  getAdminUserOperationLogs,
  forceAdminUserLogout,
  batchForceAdminUserLogout,
  getOnlineAdminUsers,
  getAdminUserStats,
  exportAdminUsers,
  importAdminUsers,
  checkAdminUsername,
  checkAdminEmail,
  sendPasswordResetEmail,
  getAdminUserSuggestions
}
