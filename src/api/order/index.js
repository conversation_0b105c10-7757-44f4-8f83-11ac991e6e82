import { http } from '../request'

/**
 * 订单管理相关API
 * 基于Swagger文档: {{host}}/admin/order/*
 */

// 获取订单列表
export const getOrderList = (params = {}) => {
  return http.get('/admin/order/list', {
    page: 1,
    limit: 10,
    ...params
  })
}

// 获取订单详情
export const getOrderDetail = (id) => {
  return http.get('/admin/order/detail', { id })
}

// 创建订单
export const createOrder = (data) => {
  return http.post('/admin/order/create', data)
}

// 更新订单
export const updateOrder = (data) => {
  return http.post('/admin/order/edit', data)
}

// 删除订单
export const deleteOrder = (id) => {
  return http.get('/admin/order/del', { id })
}

// 批量删除订单
export const batchDeleteOrders = (ids) => {
  return http.post('/admin/order/batch-delete', { ids })
}

// 更新订单状态
export const updateOrderStatus = (id, status) => {
  return http.post('/admin/order/status', { id, status })
}

// 批量更新订单状态
export const batchUpdateOrderStatus = (ids, status) => {
  return http.post('/admin/order/batch-status', { ids, status })
}

// 导出订单数据
export const exportOrders = (params = {}) => {
  return http.get('/admin/order/export', params, {
    responseType: 'blob'
  })
}

// 获取订单统计数据
export const getOrderStats = (params = {}) => {
  return http.get('/admin/order/stats', params)
}

// 订单搜索建议
export const getOrderSuggestions = (keyword) => {
  return http.get('/admin/order/suggestions', { keyword })
}

export default {
  getOrderList,
  getOrderDetail,
  createOrder,
  updateOrder,
  deleteOrder,
  batchDeleteOrders,
  updateOrderStatus,
  batchUpdateOrderStatus,
  exportOrders,
  getOrderStats,
  getOrderSuggestions
}
