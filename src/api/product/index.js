import { http } from '../request'

/**
 * 商品管理相关API
 * 基于Swagger文档: {{host}}/admin/product/*
 */

// 获取商品列表
export const getProductList = (params = {}) => {
  return http.get('/admin/product/list', {
    page: 1,
    limit: 10,
    ...params
  })
}

// 获取商品详情
export const getProductDetail = (id) => {
  return http.get('/admin/product/detail', { id })
}

// 创建商品
export const createProduct = (data) => {
  return http.post('/admin/product/create', data)
}

// 更新商品
export const updateProduct = (data) => {
  return http.post('/admin/product/edit', data)
}

// 删除商品
export const deleteProduct = (id) => {
  return http.get('/admin/product/del', { id })
}

// 批量删除商品
export const batchDeleteProducts = (ids) => {
  return http.post('/admin/product/batch-delete', { ids })
}

// 更新商品状态（上架/下架）
export const updateProductStatus = (id, status) => {
  return http.post('/admin/product/status', { id, status })
}

// 批量更新商品状态
export const batchUpdateProductStatus = (ids, status) => {
  return http.post('/admin/product/batch-status', { ids, status })
}

// 上传商品图片
export const uploadProductImage = (file) => {
  const formData = new FormData()
  formData.append('image', file)
  return http.upload('/admin/product/upload-image', formData)
}

// 批量上传商品图片
export const batchUploadProductImages = (files) => {
  const formData = new FormData()
  files.forEach((file, index) => {
    formData.append(`images[${index}]`, file)
  })
  return http.upload('/admin/product/batch-upload-images', formData)
}

// 获取商品分类列表
export const getProductCategories = () => {
  return http.get('/admin/product/categories')
}

// 创建商品分类
export const createProductCategory = (data) => {
  return http.post('/admin/product/category/create', data)
}

// 更新商品分类
export const updateProductCategory = (id, data) => {
  return http.post('/admin/product/category/edit', { id, ...data })
}

// 删除商品分类
export const deleteProductCategory = (id) => {
  return http.get('/admin/product/category/del', { id })
}

// 获取商品时间段列表
export const getProductTimeSlots = () => {
  return http.get('/admin/product/time-slots')
}

// 创建商品时间段
export const createProductTimeSlot = (data) => {
  return http.post('/admin/product/time-slot/create', data)
}

// 更新商品时间段
export const updateProductTimeSlot = (id, data) => {
  return http.post('/admin/product/time-slot/edit', { id, ...data })
}

// 删除商品时间段
export const deleteProductTimeSlot = (id) => {
  return http.get('/admin/product/time-slot/del', { id })
}

// 导出商品数据
export const exportProducts = (params = {}) => {
  return http.get('/admin/product/export', params, {
    responseType: 'blob'
  })
}

// 导入商品数据
export const importProducts = (file) => {
  const formData = new FormData()
  formData.append('file', file)
  return http.upload('/admin/product/import', formData)
}

// 获取商品统计数据
export const getProductStats = (params = {}) => {
  return http.get('/admin/product/stats', params)
}

// 商品搜索建议
export const getProductSuggestions = (keyword) => {
  return http.get('/admin/product/suggestions', { keyword })
}

// 复制商品
export const copyProduct = (id) => {
  return http.post('/admin/product/copy', { id })
}

export default {
  getProductList,
  getProductDetail,
  createProduct,
  updateProduct,
  deleteProduct,
  batchDeleteProducts,
  updateProductStatus,
  batchUpdateProductStatus,
  uploadProductImage,
  batchUploadProductImages,
  getProductCategories,
  createProductCategory,
  updateProductCategory,
  deleteProductCategory,
  getProductTimeSlots,
  createProductTimeSlot,
  updateProductTimeSlot,
  deleteProductTimeSlot,
  exportProducts,
  importProducts,
  getProductStats,
  getProductSuggestions,
  copyProduct
}
