/**
 * 配置管理模块路由配置
 * 包含配置列表、新增配置、编辑配置等功能
 */

// 路由懒加载配置
const ConfigList = () => import('@/views/config/config_list.vue')
const ConfigCreate = () => import('@/views/config/config_create.vue')
const ConfigEdit = () => import('@/views/config/config_edit.vue')

// 配置管理模块配置
export const configModule = {
  // 模块名称，用于从 src/config/modules.js 获取配置
  name: 'config',

  // 路由配置
  routes: [
    {
      path: 'config-list',
      name: 'ConfigList',
      component: ConfigList,
      meta: {
        title: '配置列表',
        requireAuth: true,
        icon: 'Tools',
        permission: 'config:view',
        showInMenu: true,
        order: 1
      },
      children: [
        {
          path: 'config-create',
          name: 'ConfigCreate',
          component: ConfigCreate,
          meta: {
            title: '新增配置',
            requireAuth: true,
            icon: 'Plus',
            permission: 'config:create',
            showInMenu: false,
            parent: 'ConfigList'
          }
        },
        {
          path: 'config-edit/:id',
          name: 'ConfigEdit',
          component: ConfigEdit,
          meta: {
            title: '编辑配置',
            requireAuth: true,
            icon: 'EditPen',
            permission: 'config:edit',
            showInMenu: false,
            parent: 'ConfigList'
          }
        }
      ]
    }
  ]
}

export default configModule
