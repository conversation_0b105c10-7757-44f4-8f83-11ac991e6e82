/**
 * 订单管理模块路由配置
 * 包含订单列表、新增订单等功能
 */

// 路由懒加载配置
const OrderList = () => import('@/views/order/order_list.vue')
const OrderCreate = () => import('@/views/order/order_create.vue')

// 订单管理模块配置
export const orderModule = {
  // 模块名称，用于从 src/config/modules.js 获取配置
  name: 'order',

  // 路由配置
  routes: [
    {
      path: 'order-list',
      name: 'OrderList',
      component: OrderList,
      meta: {
        title: '订单列表',
        requireAuth: true,
        icon: 'List',
        permission: 'order:view',
        showInMenu: true,
        order: 1
      },
      children: [
        {
          path: 'order-create',
          name: 'OrderCreate',
          component: OrderCreate,
          meta: {
            title: '新增订单',
            requireAuth: true,
            icon: 'Plus',
            permission: 'order:create',
            showInMenu: false,
            parent: 'OrderList'
          }
        }
      ]
    }
  ]
}

export default orderModule
