/**
 * 演示模块路由配置
 * 包含表单、表格、组件演示等功能
 */

// 路由懒加载配置
const Form = () => import('@/views/demo/Form.vue')
const Table = () => import('@/views/demo/Table.vue')
const ComponentDemo = () => import('@/views/demo/ComponentDemo.vue')
const SimpleDemo = () => import('@/views/demo/SimpleDemo.vue')
const MenuDemo = () => import('@/views/demo/MenuDemo.vue')

// 演示模块配置
export const demoModule = {
  // 模块名称，用于从 src/config/modules.js 获取配置
  name: 'demo',

  // 路由配置
  routes: [
    {
      path: 'form',
      name: 'Form',
      component: Form,
      meta: {
        title: '表单管理',
        requireAuth: true,
        icon: 'Edit',
        permission: 'form:manage',
        showInMenu: true,
        order: 1
      }
    },
    {
      path: 'table',
      name: 'Table',
      component: Table,
      meta: {
        title: '数据表格',
        requireAuth: true,
        icon: 'Grid',
        permission: 'table:manage',
        showInMenu: true,
        order: 2
      }
    },
    {
      path: 'component-demo',
      name: 'ComponentDemo',
      component: ComponentDemo,
      meta: {
        title: '组件演示',
        requireAuth: true,
        icon: 'Operation',
        permission: ['form:manage', 'table:manage'],
        showInMenu: true,
        order: 3
      }
    },
    {
      path: 'simple-demo',
      name: 'SimpleDemo',
      component: SimpleDemo,
      meta: {
        title: '超简化组件',
        requireAuth: true,
        icon: 'MagicStick',
        permission: ['form:manage', 'table:manage'],
        showInMenu: true,
        order: 4
      }
    },
    {
      path: 'menu-demo',
      name: 'MenuDemo',
      component: MenuDemo,
      meta: {
        title: '菜单模式演示',
        requireAuth: true,
        icon: 'Operation',
        permission: ['form:manage', 'table:manage'],
        showInMenu: true,
        order: 5
      }
    },
    {
      path: 'module-control',
      name: 'ModuleControl',
      component: () => import('@/views/demo/ModuleControl.vue'),
      meta: {
        title: '模块控制',
        icon: 'Setting',
        permission: ['form:manage', 'table:manage'],
        showInMenu: true,
        order: 6
      }
    }
  ]
}

export default demoModule
