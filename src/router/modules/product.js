/**
 * 商品管理模块路由配置
 * 包含商品列表、新增商品等功能
 */

// 路由懒加载配置
const ProductList = () => import('@/views/product/product_list.vue')
const ProductCreate = () => import('@/views/product/product_create.vue')

// 商品管理模块配置
export const productModule = {
  // 模块名称，用于从 src/config/modules.js 获取配置
  name: 'product',

  // 路由配置
  routes: [
    {
      path: 'product-list',
      name: 'ProductList',
      component: ProductList,
      meta: {
        title: '商品列表',
        requireAuth: true,
        icon: 'ShoppingBag',
        permission: 'product:view',
        showInMenu: true,
        order: 1
      },
      children: [
        {
          path: 'product-create',
          name: 'ProductCreate',
          component: ProductCreate,
          meta: {
            title: '新增商品',
            requireAuth: true,
            icon: 'Plus',
            permission: 'product:create',
            showInMenu: false,
            parent: 'ProductList'
          }
        }
      ]
    }
  ]
}

export default productModule
