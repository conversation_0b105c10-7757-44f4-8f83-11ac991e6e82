/**
 * 后台用户管理模块路由配置
 * 包含管理员列表、新增管理员等功能
 */

// 路由懒加载配置
const AdminUserList = () => import('@/views/admin-user/admin_user_list.vue')
const AdminUserCreate = () => import('@/views/admin-user/admin_user_create.vue')

// 后台用户管理模块配置
export const adminUserModule = {
  // 模块名称，用于从 src/config/modules.js 获取配置
  name: 'adminUser',

  // 路由配置
  routes: [
    {
      path: 'admin-user-list',
      name: 'AdminUserList',
      component: AdminUserList,
      meta: {
        title: '列表',
        requireAuth: true,
        icon: 'Avatar',
        permission: 'admin_user:view',
        showInMenu: true,
        order: 1
      },
      children: [
        {
          path: 'admin-user-create',
          name: 'AdminUserCreate',
          component: AdminUserCreate,
          meta: {
            title: '新增管理员',
            requireAuth: true,
            icon: 'Plus',
            // permission: 'admin_user:create',
            showInMenu: false,
            parent: 'AdminUserList'
          }
        }
      ]
    }
  ]
}

export default adminUserModule
