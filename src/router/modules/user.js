/**
 * 用户管理模块路由配置
 * 包含用户列表、新增用户、编辑用户等功能
 */

// 路由懒加载配置
const UserList = () => import('@/views/user/UserList.vue')
const UserListV2 = () => import('@/views/user/user_list_v2.vue')
const UserCreateV2 = () => import('@/views/user/user_create_v2.vue')
const UserEdit = () => import('@/views/user/UserEdit.vue')

// 用户管理模块配置
export const userModule = {
  // 模块名称，用于从 src/config/modules.js 获取配置
  name: 'user',

  // 路由配置
  routes: [
    {
      path: 'user-list',
      name: 'UserList',
      component: UserList,
      meta: {
        title: '用户列表',
        requireAuth: true,
        icon: 'UserFilled',
        permission: 'user:view',
        showInMenu: true,
        order: 1
      },
      children: [
        {
          path: 'user-edit/:id?',
          name: 'UserEdit',
          component: UserEdit,
          meta: {
            title: '编辑用户',
            requireAuth: true,
            icon: 'EditPen',
            permission: ['user:create', 'user:edit'],
            showInMenu: false,
            parent: 'UserList'
          }
        }
      ]
    },
    {
      path: 'user-list-v2',
      name: 'UserListV2',
      component: UserListV2,
      meta: {
        title: '用户列表（简化配置）',
        requireAuth: true,
        icon: 'Avatar',
        permission: 'user:view',
        showInMenu: true,
        order: 2
      },
      children: [
        {
          path: 'user-create-v2',
          name: 'UserCreateV2',
          component: UserCreateV2,
          meta: {
            title: '新增用户（简化配置）',
            requireAuth: true,
            icon: 'Plus',
            permission: 'user:create',
            showInMenu: false,
            parent: 'UserListV2'
          }
        }
      ]
    }
  ]
}

export default userModule
