/**
 * 控制台模块路由配置
 * 包含系统首页、数据概览等
 */

// 路由懒加载配置
const Dashboard = () => import('@/views/dashboard/Dashboard.vue')
const DashboardV2 = () => import('@/views/dashboard/DashboardV2.vue')

// 控制台模块配置
export const dashboardModule = {
  // 模块名称，用于从 src/config/modules.js 获取配置
  name: 'dashboard',

  // 路由配置
  routes: [
    {
      path: 'dashboard',
      name: 'Dashboard',
      component: Dashboard,
      meta: {
        title: '控制台（经典版）',
        requireAuth: true,
        icon: 'Monitor',
        permission: 'dashboard:view',
        showInMenu: false,
        order: 1
      }
    },
    {
      path: 'dashboard-v2',
      name: 'DashboardV2',
      component: DashboardV2,
      meta: {
        title: '控制台',
        requireAuth: true,
        icon: 'Sunny',
        permission: 'dashboard:view',
        showInMenu: true,
        order: 1
      }
    }
  ]
}

export default dashboardModule
