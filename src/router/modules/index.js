/**
 * 路由模块统一导出
 * 将所有模块的路由和菜单配置集中管理
 */

import { authModule } from './auth.js'
import { dashboardModule } from './dashboard.js'
import { demoModule } from './demo.js'
import { userModule } from './user.js'
import { orderModule } from './order.js'
import { productModule } from './product.js'
import { configModule } from './config.js'
import { adminUserModule } from './admin-user.js'
import { systemModule } from './system.js'
import { advancedDemoModule } from './advanced-demo.js'
import { getModuleMenuGroup } from '@/config/modules.js'

// 所有模块配置
export const modules = [
  authModule,
  dashboardModule,
  demoModule,
  userModule,
  orderModule,
  productModule,
  configModule,
  adminUserModule,
  systemModule,
  advancedDemoModule
]

/**
 * 生成完整的路由配置
 * @returns {Array} 路由配置数组
 */
export function generateRoutes() {
  const routes = []

  // 根路径重定向
  routes.push({
    path: '/',
    redirect: '/login'
  })

  // 收集所有模块的路由
  const authRoutes = []
  const adminRoutes = []

  modules.forEach(module => {
    module.routes.forEach(route => {
      // 认证相关路由直接添加到根级别
      if (module === authModule) {
        authRoutes.push(route)
      } else {
        // 处理路由的children结构，保持层级关系
        const processedRoute = { ...route }

        // 递归处理子路由，保持嵌套结构
        const processChildren = (parentRoute) => {
          if (parentRoute.children && parentRoute.children.length > 0) {
            parentRoute.children = parentRoute.children.map(childRoute => {
              const processedChild = { ...childRoute }

              // 递归处理孙路由
              if (childRoute.children && childRoute.children.length > 0) {
                processChildren(processedChild)
              }

              return processedChild
            })
          }
          return parentRoute
        }

        adminRoutes.push(processChildren(processedRoute))
      }
    })
  })

  // 按order字段排序
  adminRoutes.sort((a, b) => {
    const orderA = a.meta?.order || 999
    const orderB = b.meta?.order || 999
    return orderA - orderB
  })

  // 添加认证路由
  routes.push(...authRoutes)

  // 添加管理后台布局路由
  const Layout = () => import('@/views/common/Layout.vue')
  routes.push({
    path: '/admin',
    name: 'Layout',
    component: Layout,
    redirect: '/admin/dashboard',
    meta: {
      title: '管理后台',
      requireAuth: true
    },
    children: adminRoutes
  })

  // 404页面
  routes.push({
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/common/NotFound.vue'),
    meta: {
      title: '页面不存在',
      requireAuth: false
    }
  })

  return routes
}

/**
 * 生成完整的菜单配置
 * 从路由配置中自动生成菜单结构，支持排序和层级
 * @param {boolean} flatMenu - 是否生成扁平化菜单（不折叠）
 * @returns {Array} 菜单配置数组
 */
export function generateMenus(flatMenu = false) {
  const menus = []

  modules.forEach(module => {
    // 跳过认证模块
    if (module === authModule) {
      return
    }

    // 从配置文件获取模块的menuGroup配置
    const menuGroup = getModuleMenuGroup(module.name)
    if (!menuGroup) {
      return // 如果没有配置，跳过这个模块
    }

    // 检查模块是否可见
    if (menuGroup.visible === false) {
      return // 如果模块设置为不可见，跳过整个模块
    }

    // 获取需要在菜单中显示的路由，按order排序
    const menuRoutes = module.routes
      .filter(route => route.meta?.showInMenu)
      .sort((a, b) => {
        const orderA = a.meta?.order || 999
        const orderB = b.meta?.order || 999
        return orderA - orderB
      })

    if (menuRoutes.length === 0) {
      return
    }

    if (flatMenu) {
      // 扁平化菜单：所有菜单项都在同一级别，按模块分组
      menuRoutes.forEach(route => {
        // 添加主菜单项
        menus.push({
          path: `/admin/${route.path}`,
          name: route.name,
          title: route.meta.title,
          icon: route.meta.icon,
          permission: route.meta.permission,
          order: route.meta.order || 999,
          moduleTitle: module.menuGroup?.title,
          level: 1 // 标记为一级菜单
        })

        // 如果有子路由且需要在菜单中显示，也添加为同级菜单
        if (route.children) {
          route.children
            .filter(child => child.meta?.showInMenu !== false)
            .forEach((child, index) => {
              menus.push({
                path: `/admin/${route.path}/${child.path}`,
                name: child.name,
                title: child.meta.title,
                icon: child.meta.icon || route.meta.icon,
                permission: child.meta.permission,
                order: (route.meta.order || 999) + 0.1 + (index * 0.01),
                moduleTitle: menuGroup.title,
                parentRoute: route.name,
                level: 2 // 标记为二级菜单
              })
            })
        }
      })
    } else {
      // 层级菜单：保持原有的折叠结构
      const groupMenu = {
        path: `group-${menuGroup.order || 999}`, // 使用唯一标识而不是中文路径
        title: menuGroup.title,
        icon: menuGroup.icon,
        permission: menuGroup.permission,
        order: menuGroup.order || 999,
          children: menuRoutes.map(route => ({
            path: `/admin/${route.path}`,
            name: route.name,
            title: route.meta.title,
            icon: route.meta.icon,
            permission: route.meta.permission,
            order: route.meta.order || 999,
            // 如果路由有children，也添加到菜单中（支持3级菜单）
            children: route.children ? route.children
              .filter(child => child.meta?.showInMenu !== false)
              .map(child => ({
                path: `/admin/${route.path}/${child.path}`,
                name: child.name,
                title: child.meta.title,
                icon: child.meta.icon,
                permission: child.meta.permission,
                parent: route.name,
                showInMenu: child.meta?.showInMenu !== false,
                order: child.meta?.order || 999,
                // 支持第三级菜单
                children: child.children ? child.children
                  .filter(grandChild => grandChild.meta?.showInMenu !== false)
                  .map(grandChild => ({
                    path: `/admin/${route.path}/${child.path}/${grandChild.path}`,
                    name: grandChild.name,
                    title: grandChild.meta.title,
                    icon: grandChild.meta.icon,
                    permission: grandChild.meta.permission,
                    parent: child.name,
                    showInMenu: grandChild.meta?.showInMenu !== false,
                    order: grandChild.meta?.order || 999
                  }))
                  .sort((a, b) => a.order - b.order) : undefined
              }))
              .sort((a, b) => a.order - b.order) : undefined
          }))
        }
        menus.push(groupMenu)
    }
  })

  // 按order字段对顶级菜单排序
  menus.sort((a, b) => {
    const orderA = a.order || 999
    const orderB = b.order || 999
    return orderA - orderB
  })

  return menus
}

export default {
  modules,
  generateRoutes,
  generateMenus
}
