/**
 * 系统管理模块路由配置
 * 包含权限测试等系统功能
 */

// 路由懒加载配置
const PermissionTest = () => import('@/views/system/PermissionTest.vue')

// 系统管理模块配置
export const systemModule = {
  // 模块名称，用于从 src/config/modules.js 获取配置
  name: 'system',

  // 路由配置
  routes: [
    {
      path: 'permission-test',
      name: 'PermissionTest',
      component: PermissionTest,
      meta: {
        title: '权限测试',
        requireAuth: true,
        icon: 'Lock',
        showInMenu: true,
        order: 1
      }
    }
  ]
}

export default systemModule
