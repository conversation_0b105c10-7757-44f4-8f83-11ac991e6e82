/**
 * 高级演示模块路由配置
 * 演示3级菜单结构的实现
 */

// 路由懒加载配置 - 使用现有组件作为演示
const Form = () => import('@/views/demo/Form.vue')
const Table = () => import('@/views/demo/Table.vue')
const ComponentDemo = () => import('@/views/demo/ComponentDemo.vue')
const SimpleDemo = () => import('@/views/demo/SimpleDemo.vue')
const MenuDemo = () => import('@/views/demo/MenuDemo.vue')

// 高级演示模块配置 - 支持3级菜单
export const advancedDemoModule = {
  // 模块名称，用于从 src/config/modules.js 获取配置
  name: 'advancedDemo',

  // 路由配置 - 3级结构演示
  routes: [
    {
      path: 'demo-forms',
      name: 'DemoForms',
      redirect: '/admin/demo-forms/basic-form', // 重定向到第一个子路由
      meta: {
        title: '表单演示',
        requireAuth: true,
        icon: 'Edit',
        permission: 'form:view',
        showInMenu: true,
        order: 1
      },
      children: [
        {
          path: 'basic-form',
          name: 'DemoBasicForm',
          component: Form,
          meta: {
            title: '基础表单',
            requireAuth: true,
            icon: 'Document',
            permission: 'form:basic',
            showInMenu: true,
            order: 1
          }
        },
        {
          path: 'advanced-form',
          name: 'DemoAdvancedForm',
          component: ComponentDemo,
          meta: {
            title: '高级表单',
            requireAuth: true,
            icon: 'DocumentChecked',
            permission: 'form:advanced',
            showInMenu: true,
            order: 2
          }
        },
        {
          path: 'step-form',
          name: 'DemoStepForm',
          component: SimpleDemo,
          meta: {
            title: '分步表单',
            requireAuth: true,
            icon: 'Guide',
            permission: 'form:step',
            showInMenu: true,
            order: 3
          }
        }
      ]
    },
    {
      path: 'demo-tables',
      name: 'DemoTables',
      redirect: '/admin/demo-tables/basic-table',
      meta: {
        title: '列表演示',
        requireAuth: true,
        icon: 'Grid',
        permission: 'table:view',
        showInMenu: true,
        order: 2
      },
      children: [
        {
          path: 'basic-table',
          name: 'DemoBasicTable',
          component: Table,
          meta: {
            title: '基础列表',
            requireAuth: true,
            icon: 'List',
            permission: 'table:basic',
            showInMenu: true,
            order: 1
          }
        },
        {
          path: 'advanced-table',
          name: 'DemoAdvancedTable',
          component: ComponentDemo,
          meta: {
            title: '高级列表',
            requireAuth: true,
            icon: 'Notebook',
            permission: 'table:advanced',
            showInMenu: true,
            order: 2
          }
        },
        {
          path: 'editable-table',
          name: 'DemoEditableTable',
          component: SimpleDemo,
          meta: {
            title: '可编辑列表',
            requireAuth: true,
            icon: 'EditPen',
            permission: 'table:editable',
            showInMenu: true,
            order: 3
          }
        }
      ]
    },
    {
      path: 'demo-charts',
      name: 'DemoCharts',
      redirect: '/admin/demo-charts/line-chart',
      meta: {
        title: '图表演示',
        requireAuth: true,
        icon: 'TrendCharts',
        permission: 'chart:view',
        showInMenu: true,
        order: 3
      },
      children: [
        {
          path: 'line-chart',
          name: 'DemoLineChart',
          component: ComponentDemo,
          meta: {
            title: '折线图',
            requireAuth: true,
            icon: 'DataLine',
            permission: 'chart:line',
            showInMenu: true,
            order: 1
          }
        },
        {
          path: 'bar-chart',
          name: 'DemoBarChart',
          component: SimpleDemo,
          meta: {
            title: '柱状图',
            requireAuth: true,
            icon: 'DataBoard',
            permission: 'chart:bar',
            showInMenu: true,
            order: 2
          }
        },
        {
          path: 'pie-chart',
          name: 'DemoPieChart',
          component: MenuDemo,
          meta: {
            title: '饼图',
            requireAuth: true,
            icon: 'PieChart',
            permission: 'chart:pie',
            showInMenu: true,
            order: 3
          }
        }
      ]
    }
  ]
}

export default advancedDemoModule
