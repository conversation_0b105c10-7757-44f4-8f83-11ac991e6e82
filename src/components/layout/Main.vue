<template>
  <div class="main-content">
    <!-- 路由视图 -->
    <router-view v-slot="{ Component, route }">
      <transition name="fade-transform" mode="out-in" appear>
        <keep-alive :include="cachedViews">
          <component :is="Component" :key="route.path" />
        </keep-alive>
      </transition>
    </router-view>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

// 缓存的视图组件名称列表
const cachedViews = ref(['Form', 'Table'])

// 监听路由变化，动态管理缓存
watch(
  () => route.name,
  (newRouteName) => {
    // 根据路由配置决定是否缓存该页面
    if (route.meta?.keepAlive && !cachedViews.value.includes(newRouteName)) {
      cachedViews.value.push(newRouteName)
    }
  }
)
</script>

<style scoped>
.main-content {
  height: 100%;
  overflow-y: auto;
  position: relative;
  min-height: 100%;
  padding: 20px;
}

/* 用户管理页面特殊样式 */
:deep(.user-list-page),
:deep(.user-edit-page) {
  margin: -20px;
  padding: 20px;
  background: transparent;
  min-height: calc(100% + 40px);
}

/* 页面切换动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* 滚动条样式 */
.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 0;
  }
}
</style>
