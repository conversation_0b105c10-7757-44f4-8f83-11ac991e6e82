<template>
  <div class="top-navigation">
    <!-- 左侧操作区 -->
    <div class="top-left">
      <!-- 侧边栏折叠按钮 -->
      <el-button
        link
        class="collapse-btn"
        @click="handleToggleSidebar"
      >
        <el-icon size="18">
          <Fold v-if="!collapse" />
          <Expand v-else />
        </el-icon>
      </el-button>


      
      <!-- 面包屑导航 -->
      <el-breadcrumb separator="/" class="breadcrumb">
        <el-breadcrumb-item
          v-for="(breadcrumb, index) in breadcrumbs"
          :key="index"
          :to="breadcrumb.path && index < breadcrumbs.length - 1 ? { path: breadcrumb.path } : null"
        >
          {{ breadcrumb.title }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 右侧用户信息区 -->
    <div class="top-right">
      <!-- 通知图标 -->
      <el-popover
        placement="bottom"
        :width="300"
        trigger="click"
        @show="handleNotificationShow"
      >
        <template #reference>
          <el-badge :value="notificationCount" :max="99" class="notification-badge">
            <el-button link class="icon-btn">
              <el-icon size="18">
                <Bell />
              </el-icon>
            </el-button>
          </el-badge>
        </template>
        
        <!-- 通知列表 -->
        <div class="notification-panel">
          <div class="notification-header">
            <span class="notification-title">通知消息</span>
            <el-button link size="small" @click="handleClearAll">清空全部</el-button>
          </div>
          
          <div class="notification-list">
            <div 
              v-for="notification in notifications" 
              :key="notification.id"
              class="notification-item"
              :class="{ 'unread': !notification.read }"
              @click="handleNotificationClick(notification)"
            >
              <div class="notification-content">
                <div class="notification-title-text">{{ notification.title }}</div>
                <div class="notification-message">{{ notification.message }}</div>
                <div class="notification-time">{{ notification.time }}</div>
              </div>
              <div v-if="!notification.read" class="notification-dot"></div>
            </div>
            
            <div v-if="notifications.length === 0" class="notification-empty">
              <el-icon><Bell /></el-icon>
              <span>暂无通知消息</span>
            </div>
          </div>
          
          <div class="notification-footer">
            <el-button link size="small" @click="handleViewAll">查看全部</el-button>
          </div>
        </div>
      </el-popover>

      <!-- 全屏切换 -->
      <el-button link class="icon-btn" @click="toggleFullscreen">
        <el-icon size="18">
          <FullScreen v-if="!isFullscreen" />
          <Aim v-else />
        </el-icon>
      </el-button>

      <!-- 用户信息下拉菜单 -->
      <el-dropdown trigger="click" @command="handleUserCommand">
        <div class="user-info">
          <el-avatar :size="32" :src="userInfo.avatar || ''">
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="username">{{ userInfo.nickname || userInfo.username }}</span>
          <el-icon class="dropdown-icon">
            <ArrowDown />
          </el-icon>
        </div>
        
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              个人中心
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              系统设置
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Fold,
  Expand,
  Bell,
  FullScreen,
  Aim,
  User,
  ArrowDown,
  Setting,
  SwitchButton
} from '@element-plus/icons-vue'
import { getUserInfo, logout } from '@/router/index.js'
import { generateBreadcrumbs } from '@/utils/breadcrumb.js'

const props = defineProps({
  collapse: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['toggle-sidebar'])

const route = useRoute()

// 用户信息
const userInfo = ref({})

// 通知相关
const notificationCount = ref(3)
const notifications = ref([
  {
    id: 1,
    title: '系统更新',
    message: '系统将于今晚22:00-24:00进行维护更新',
    time: '2小时前',
    read: false
  },
  {
    id: 2,
    title: '新用户注册',
    message: '用户"张三"已成功注册并等待审核',
    time: '1天前',
    read: false
  },
  {
    id: 3,
    title: '数据备份完成',
    message: '系统数据备份已完成，备份文件已保存',
    time: '2天前',
    read: true
  }
])

// 全屏状态
const isFullscreen = ref(false)


// 面包屑导航 - 使用工具函数自动生成
const breadcrumbs = computed(() => {
  return generateBreadcrumbs(route)
})

// 切换侧边栏
const handleToggleSidebar = () => {
  emit('toggle-sidebar')
}



// 处理用户下拉菜单命令
const handleUserCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人中心功能开发中...')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中...')
      break
    case 'logout':
      await handleLogout()
      break
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    logout()
  } catch (error) {
    // 用户取消操作
  }
}

// 切换全屏
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 初始化用户信息
const initUserInfo = () => {
  const user = getUserInfo()
  if (user) {
    userInfo.value = user
  }
}

// 页面挂载时初始化
onMounted(() => {
  initUserInfo()
  document.addEventListener('fullscreenchange', handleFullscreenChange)
})

// 通知相关方法
const handleNotificationShow = () => {
  console.log('显示通知面板')
}

const handleNotificationClick = (notification) => {
  // 标记为已读
  notification.read = true
  updateNotificationCount()
  ElMessage.info(`查看通知：${notification.title}`)
}

const handleClearAll = () => {
  notifications.value = []
  notificationCount.value = 0
  ElMessage.success('已清空所有通知')
}

const handleViewAll = () => {
  ElMessage.info('跳转到通知中心...')
}

// 更新通知数量
const updateNotificationCount = () => {
  notificationCount.value = notifications.value.filter(n => !n.read).length
}

// 页面卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<style scoped>
.top-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 100%;
  background-color: #fff;
}

.top-left {
  display: flex;
  align-items: center;
}

.collapse-btn,
.menu-mode-btn {
  margin-right: 20px;
  color: #606266;
}

.collapse-btn:hover,
.menu-mode-btn:hover {
  color: #409eff;
}

.breadcrumb {
  font-size: 14px;
}

.top-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.notification-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.icon-btn {
  color: #606266;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 34px;
  min-height: 34px;
}

.icon-btn:hover {
  color: #409eff;
  background-color: #f5f7fa;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #303133;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-icon {
  color: #909399;
  transition: transform 0.3s;
}

/* 下拉菜单项样式 */
:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
}

:deep(.el-dropdown-menu__item .el-icon) {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-navigation {
    padding: 0 15px;
  }
  
  .breadcrumb {
    display: none;
  }
  
  .username {
    display: none;
  }
  
  .top-right {
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .notification-badge {
    display: none;
  }
  
  .icon-btn {
    padding: 6px;
  }
}

/* 通知面板样式 */
.notification-panel {
  padding: 0;
  max-height: 400px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafafa;
}

.notification-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.notification-list {
  max-height: 280px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 16px;
  border-bottom: 1px solid #f5f7fa;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
}

.notification-item:hover {
  background-color: #f5f7fa;
}

.notification-item.unread {
  background-color: #fdf6ec;
}

.notification-item.unread:hover {
  background-color: #faecd8;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title-text {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.notification-message {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.notification-time {
  font-size: 11px;
  color: #909399;
}

.notification-dot {
  width: 6px;
  height: 6px;
  background-color: #f56c6c;
  border-radius: 50%;
  margin-left: 8px;
  margin-top: 4px;
  flex-shrink: 0;
}

.notification-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
}

.notification-empty .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.notification-empty span {
  font-size: 14px;
}

.notification-footer {
  padding: 8px 16px;
  border-top: 1px solid #ebeef5;
  background-color: #fafafa;
  text-align: center;
}

/* 徽章样式优化 */
:deep(.el-badge__content) {
  border: 1px solid #fff;
  font-size: 11px;
  min-width: 16px;
  height: 16px;
  line-height: 14px;
}

/* 滚动条样式 */
.notification-list::-webkit-scrollbar {
  width: 4px;
}

.notification-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.notification-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.notification-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
