<template>
  <el-table
    ref="tableRef"
    :data="data"
    :loading="loading"
    :stripe="config.stripe"
    :border="config.border"
    @selection-change="$emit('selection-change', $event)"
    @sort-change="$emit('sort-change', $event)"
    @row-click="$emit('row-click', $event)"
  >
    <!-- 选择列 -->
    <el-table-column
      v-if="config.selectable"
      type="selection"
      width="55"
    />

    <!-- 动态列 -->
    <el-table-column
      v-for="column in config.columns"
      :key="column.prop"
      :prop="column.prop"
      :label="column.label"
      :width="column.width"
      :min-width="column.minWidth"
      :sortable="column.sortable"
    >
      <template #default="{ row }">
        <!-- 标签类型 -->
        <el-tag
          v-if="column.type === 'tag'"
          :type="column.tagMap?.[row[column.prop]]?.type || 'primary'"
        >
          {{ column.tagMap?.[row[column.prop]]?.text || row[column.prop] }}
        </el-tag>

        <!-- 图片类型 -->
        <el-image
          v-else-if="column.type === 'image'"
          :src="row[column.prop]"
          style="width: 60px; height: 60px"
          fit="cover"
        />

        <!-- 货币类型 -->
        <span v-else-if="column.type === 'currency'">
          ¥{{ row[column.prop] }}
        </span>

        <!-- 日期时间类型 -->
        <span v-else-if="column.type === 'datetime'">
          {{ formatDateTime(row[column.prop]) }}
        </span>

        <!-- 操作按钮 -->
        <div v-else-if="column.type === 'actions'" class="action-buttons">
          <el-button
            v-for="action in column.actions"
            :key="action.type"
            :type="action.style"
            size="small"
            @click="handleAction(action.type, row)"
          >
            {{ action.label }}
          </el-button>
        </div>

        <!-- 默认文本 -->
        <span v-else>{{ row[column.prop] }}</span>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['selection-change', 'sort-change', 'row-click', 'edit', 'delete'])

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 处理操作按钮点击
const handleAction = (type, row) => {
  emit(type, row)
}
</script>

<style scoped>
.action-buttons {
  display: flex;
  gap: 5px;
}
</style>
