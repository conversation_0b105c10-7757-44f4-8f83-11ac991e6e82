/**
 * 表格配置工具函数
 * 提供简化的表格配置方法和智能类型推断
 */

import { COMMON_TAG_MAPS, COMMON_ACTIONS } from './table-presets.js'

/**
 * 快速创建表格列配置
 * @param {string} prop 字段名
 * @param {string} label 列标题
 * @param {object} options 配置选项
 * @returns {object} 列配置对象
 */
export function createColumn(prop, label, options = {}) {
  const column = {
    prop,
    label,
    ...options
  }

  // 智能推断列类型和配置
  if (!column.type) {
    column.type = inferColumnType(prop, options)
  }

  // 根据类型设置默认配置
  applyColumnDefaults(column)

  return column
}

/**
 * 智能推断列类型
 * @param {string} prop 字段名
 * @param {object} options 配置选项
 * @returns {string} 列类型
 */
function inferColumnType(prop, options) {
  // 根据字段名推断类型
  const fieldNamePatterns = {
    image: /^(avatar|image|img|photo|picture|pic)$/i,
    datetime: /^(time|date|created|updated|login|publish).*$/i,
    currency: /^(price|amount|money|cost|fee|total)$/i,
    tag: /^(status|state|type|category|role|level|grade)$/i
  }

  for (const [type, pattern] of Object.entries(fieldNamePatterns)) {
    if (pattern.test(prop)) {
      return type
    }
  }

  // 根据配置选项推断
  if (options.tagMap || options.statusMap) return 'tag'
  if (options.imageStyle || options.preview) return 'image'
  if (options.format === 'currency') return 'currency'
  if (options.format === 'datetime') return 'datetime'

  return 'text'
}

/**
 * 根据列类型应用默认配置
 * @param {object} column 列配置对象
 */
function applyColumnDefaults(column) {
  const defaults = {
    image: {
      width: 80,
      imageStyle: { width: '40px', height: '40px' }
    },
    datetime: {
      width: 160
    },
    currency: {
      width: 120
    },
    tag: {
      width: 100,
      tagMap: inferTagMap(column.prop)
    },
    text: {}
  }

  const typeDefaults = defaults[column.type] || {}
  Object.keys(typeDefaults).forEach(key => {
    if (column[key] === undefined) {
      column[key] = typeDefaults[key]
    }
  })
}

/**
 * 推断标签映射
 * @param {string} prop 字段名
 * @returns {object} 标签映射
 */
function inferTagMap(prop) {
  const tagMapPatterns = {
    status: COMMON_TAG_MAPS.status,
    role: COMMON_TAG_MAPS.userRole,
    orderStatus: COMMON_TAG_MAPS.orderStatus,
    articleStatus: COMMON_TAG_MAPS.articleStatus
  }

  // 根据字段名推断合适的标签映射
  if (/^(status|state)$/i.test(prop)) return COMMON_TAG_MAPS.status
  if (/role/i.test(prop)) return COMMON_TAG_MAPS.userRole
  if (/order.*status/i.test(prop)) return COMMON_TAG_MAPS.orderStatus
  if (/article.*status/i.test(prop)) return COMMON_TAG_MAPS.articleStatus

  return COMMON_TAG_MAPS.status // 默认使用通用状态映射
}

/**
 * 批量创建表格列
 * @param {array} columnDefs 列定义数组
 * @returns {array} 列配置数组
 */
export function createColumns(columnDefs) {
  return columnDefs.map(def => {
    if (typeof def === 'string') {
      // 简单字符串定义，自动生成标题
      return createColumn(def, generateLabel(def))
    } else if (Array.isArray(def)) {
      // 数组定义：[prop, label, options]
      const [prop, label, options = {}] = def
      return createColumn(prop, label, options)
    } else if (typeof def === 'object') {
      // 对象定义
      const { prop, label, ...options } = def
      return createColumn(prop, label || generateLabel(prop), options)
    }
    return def
  })
}

/**
 * 根据字段名生成标题
 * @param {string} prop 字段名
 * @returns {string} 生成的标题
 */
function generateLabel(prop) {
  const labelMap = {
    id: 'ID',
    name: '名称',
    title: '标题',
    username: '用户名',
    email: '邮箱',
    phone: '手机号',
    avatar: '头像',
    image: '图片',
    status: '状态',
    role: '角色',
    createTime: '创建时间',
    updateTime: '更新时间',
    lastLoginTime: '最后登录',
    price: '价格',
    amount: '金额',
    category: '分类',
    description: '描述'
  }

  return labelMap[prop] || prop
}

/**
 * 快速创建操作列
 * @param {string|array} actionType 操作类型或自定义操作数组
 * @param {object} options 配置选项
 * @returns {object} 操作列配置
 */
export function createActionColumn(actionType = 'basic', options = {}) {
  let actions = []

  if (typeof actionType === 'string') {
    actions = COMMON_ACTIONS[actionType] || COMMON_ACTIONS.basic
  } else if (Array.isArray(actionType)) {
    actions = actionType
  }

  return {
    prop: 'actions',
    label: '操作',
    width: 200,
    type: 'actions',
    actions: actions,
    ...options
  }
}

/**
 * 创建简化的表格配置
 * @param {object} config 配置对象
 * @returns {object} 完整的表格配置
 */
export function createTableConfig(config) {
  const {
    columns = [],
    actions = 'basic',
    pagination = true,
    selection = false,
    border = true,
    stripe = true,
    ...otherConfig
  } = config

  const tableConfig = {
    columns: createColumns(columns),
    border,
    stripe,
    ...otherConfig
  }

  // 添加操作列
  if (actions) {
    tableConfig.actions = typeof actions === 'string' 
      ? COMMON_ACTIONS[actions] || COMMON_ACTIONS.basic
      : actions
  }

  // 添加分页配置
  if (pagination) {
    tableConfig.pagination = typeof pagination === 'object' 
      ? { show: true, pageSize: 10, pageSizes: [10, 20, 50, 100], ...pagination }
      : { show: true, pageSize: 10, pageSizes: [10, 20, 50, 100] }
  }

  // 添加选择列
  if (selection) {
    tableConfig.selection = true
  }

  return tableConfig
}

/**
 * 扩展列配置
 * @param {array} columns 原始列配置
 * @param {array} extensions 扩展配置
 * @returns {array} 扩展后的列配置
 */
export function extendColumns(columns, extensions) {
  const result = [...columns]
  
  extensions.forEach(ext => {
    const { prop, position = 'append', ...config } = ext
    
    if (position === 'prepend') {
      result.unshift(createColumn(prop, config.label || generateLabel(prop), config))
    } else if (position === 'append') {
      result.push(createColumn(prop, config.label || generateLabel(prop), config))
    } else if (typeof position === 'number') {
      result.splice(position, 0, createColumn(prop, config.label || generateLabel(prop), config))
    }
  })
  
  return result
}

/**
 * 移除指定列
 * @param {array} columns 列配置数组
 * @param {array} propsToRemove 要移除的字段名数组
 * @returns {array} 移除后的列配置
 */
export function removeColumns(columns, propsToRemove) {
  return columns.filter(col => !propsToRemove.includes(col.prop))
}

/**
 * 修改列配置
 * @param {array} columns 原始列配置
 * @param {object} modifications 修改配置对象 {prop: newConfig}
 * @returns {array} 修改后的列配置
 */
export function modifyColumns(columns, modifications) {
  return columns.map(col => {
    if (modifications[col.prop]) {
      return { ...col, ...modifications[col.prop] }
    }
    return col
  })
}

/**
 * 根据权限过滤操作按钮
 * @param {array} actions 操作按钮数组
 * @param {array} userPermissions 用户权限数组
 * @returns {array} 过滤后的操作按钮
 */
export function filterActionsByPermission(actions, userPermissions) {
  return actions.filter(action => {
    if (!action.permission) return true
    
    if (Array.isArray(action.permission)) {
      return action.permission.some(perm => userPermissions.includes(perm))
    } else {
      return userPermissions.includes(action.permission)
    }
  })
}

/**
 * 创建响应式表格配置
 * @param {object} baseConfig 基础配置
 * @param {object} responsiveRules 响应式规则
 * @returns {object} 响应式表格配置
 */
export function createResponsiveTableConfig(baseConfig, responsiveRules = {}) {
  const config = { ...baseConfig }
  
  // 根据屏幕尺寸调整列配置
  const updateConfigForViewport = () => {
    const viewport = window.innerWidth
    
    if (viewport < 768 && responsiveRules.mobile) {
      Object.assign(config, responsiveRules.mobile)
    } else if (viewport < 1024 && responsiveRules.tablet) {
      Object.assign(config, responsiveRules.tablet)
    } else if (responsiveRules.desktop) {
      Object.assign(config, responsiveRules.desktop)
    }
    
    return config
  }
  
  // 初始化配置
  updateConfigForViewport()
  
  // 监听窗口大小变化
  if (typeof window !== 'undefined') {
    window.addEventListener('resize', updateConfigForViewport)
  }
  
  return config
}

/**
 * 验证表格配置
 * @param {object} config 表格配置
 * @returns {object} 验证结果
 */
export function validateTableConfig(config) {
  const errors = []
  const warnings = []
  
  // 检查必要字段
  if (!config.columns || !Array.isArray(config.columns)) {
    errors.push('columns 字段是必需的，且必须是数组')
  }
  
  // 检查列配置
  if (config.columns) {
    config.columns.forEach((col, index) => {
      if (!col.prop) {
        errors.push(`第${index + 1}列缺少 prop 字段`)
      }
      if (!col.label) {
        warnings.push(`第${index + 1}列缺少 label 字段`)
      }
    })
  }
  
  // 检查操作配置
  if (config.actions && !Array.isArray(config.actions)) {
    errors.push('actions 字段必须是数组')
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}
