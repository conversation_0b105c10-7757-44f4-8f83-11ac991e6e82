/**
 * 表格配置预设模板
 * 提供常用表格配置，简化表格使用
 */

// 常用标签映射
export const COMMON_TAG_MAPS = {
  // 状态标签
  status: {
    '1': { type: 'success', text: '启用' },
    '0': { type: 'danger', text: '禁用' },
    'active': { type: 'success', text: '活跃' },
    'inactive': { type: 'info', text: '非活跃' },
    'enabled': { type: 'success', text: '已启用' },
    'disabled': { type: 'danger', text: '已禁用' }
  },
  
  // 用户角色标签
  userRole: {
    'admin': { type: 'danger', text: '管理员' },
    'user': { type: 'primary', text: '普通用户' },
    'editor': { type: 'warning', text: '编辑员' },
    'viewer': { type: 'info', text: '访客' },
    'guest': { type: 'info', text: '游客' }
  },
  
  // 订单状态标签
  orderStatus: {
    'pending': { type: 'warning', text: '待支付' },
    'paid': { type: 'success', text: '已支付' },
    'shipped': { type: 'primary', text: '已发货' },
    'delivered': { type: 'success', text: '已送达' },
    'cancelled': { type: 'danger', text: '已取消' },
    'refunded': { type: 'info', text: '已退款' }
  },
  
  // 文章状态标签
  articleStatus: {
    'draft': { type: 'info', text: '草稿' },
    'published': { type: 'success', text: '已发布' },
    'reviewing': { type: 'warning', text: '审核中' },
    'rejected': { type: 'danger', text: '已拒绝' }
  },
  
  // 审核状态标签
  auditStatus: {
    'pending': { type: 'warning', text: '待审核' },
    'approved': { type: 'success', text: '已通过' },
    'rejected': { type: 'danger', text: '已拒绝' }
  }
}

// 常用操作按钮配置
export const COMMON_ACTIONS = {
  // 基础操作
  basic: [
    {
      type: 'view',
      label: '查看',
      style: 'text',
      icon: 'View'
    },
    {
      type: 'edit',
      label: '编辑',
      style: 'primary',
      icon: 'Edit'
    },
    {
      type: 'delete',
      label: '删除',
      style: 'danger',
      icon: 'Delete',
      confirm: true,
      confirmText: '确定要删除吗？'
    }
  ],
  
  // 用户管理操作
  user: [
    {
      type: 'view',
      label: '查看',
      style: 'text',
      icon: 'View'
    },
    {
      type: 'edit',
      label: '编辑',
      style: 'primary',
      icon: 'Edit',
      permission: 'user:edit'
    },
    {
      type: 'resetPassword',
      label: '重置密码',
      style: 'warning',
      icon: 'Lock',
      permission: 'user:resetPassword'
    },
    {
      type: 'delete',
      label: '删除',
      style: 'danger',
      icon: 'Delete',
      confirm: true,
      confirmText: '确定要删除该用户吗？',
      permission: 'user:delete'
    }
  ],
  
  // 订单管理操作
  order: [
    {
      type: 'view',
      label: '查看详情',
      style: 'text',
      icon: 'View'
    },
    {
      type: 'ship',
      label: '发货',
      style: 'primary',
      icon: 'Van',
      permission: 'order:ship'
    },
    {
      type: 'refund',
      label: '退款',
      style: 'warning',
      icon: 'RefreshLeft',
      confirm: true,
      confirmText: '确定要为此订单退款吗？',
      permission: 'order:refund'
    }
  ],
  
  // 文章管理操作
  article: [
    {
      type: 'view',
      label: '预览',
      style: 'text',
      icon: 'View'
    },
    {
      type: 'edit',
      label: '编辑',
      style: 'primary',
      icon: 'Edit',
      permission: 'article:edit'
    },
    {
      type: 'publish',
      label: '发布',
      style: 'success',
      icon: 'Promotion',
      permission: 'article:publish'
    },
    {
      type: 'delete',
      label: '删除',
      style: 'danger',
      icon: 'Delete',
      confirm: true,
      confirmText: '确定要删除该文章吗？',
      permission: 'article:delete'
    }
  ]
}

// 预设表格配置模板
export const TABLE_PRESETS = {
  // 用户表格预设
  user: {
    columns: [
      { prop: 'id', label: 'ID', width: 80 },
      { 
        prop: 'avatar', 
        label: '头像', 
        width: 80, 
        type: 'image',
        imageStyle: { width: '40px', height: '40px', borderRadius: '50%' }
      },
      { prop: 'username', label: '用户名', width: 120 },
      { prop: 'realName', label: '真实姓名', width: 120 },
      { prop: 'email', label: '邮箱', width: 200 },
      { prop: 'phone', label: '手机号', width: 130 },
      { 
        prop: 'role', 
        label: '角色', 
        width: 100, 
        type: 'tag',
        tagMap: COMMON_TAG_MAPS.userRole
      },
      { 
        prop: 'status', 
        label: '状态', 
        width: 100, 
        type: 'tag',
        tagMap: COMMON_TAG_MAPS.status
      },
      { prop: 'createTime', label: '创建时间', width: 160, type: 'datetime' },
      { prop: 'lastLoginTime', label: '最后登录', width: 160, type: 'datetime' }
    ],
    actions: COMMON_ACTIONS.user,
    pagination: { show: true, pageSize: 10, pageSizes: [10, 20, 50, 100] },
    selection: true,
    border: true,
    stripe: true
  },
  
  // 订单表格预设
  order: {
    columns: [
      { prop: 'orderNo', label: '订单号', width: 180 },
      { prop: 'userName', label: '用户', width: 120 },
      { prop: 'productName', label: '商品', width: 200 },
      { prop: 'amount', label: '金额', width: 120, type: 'currency' },
      { 
        prop: 'status', 
        label: '状态', 
        width: 100, 
        type: 'tag',
        tagMap: COMMON_TAG_MAPS.orderStatus
      },
      { prop: 'createTime', label: '下单时间', width: 160, type: 'datetime' },
      { prop: 'payTime', label: '支付时间', width: 160, type: 'datetime' }
    ],
    actions: COMMON_ACTIONS.order,
    pagination: { show: true, pageSize: 10, pageSizes: [10, 20, 50] },
    border: true,
    stripe: true
  },
  
  // 文章表格预设
  article: {
    columns: [
      { prop: 'id', label: 'ID', width: 80 },
      { prop: 'title', label: '标题', minWidth: 200 },
      { prop: 'author', label: '作者', width: 120 },
      { prop: 'category', label: '分类', width: 120 },
      { 
        prop: 'status', 
        label: '状态', 
        width: 100, 
        type: 'tag',
        tagMap: COMMON_TAG_MAPS.articleStatus
      },
      { prop: 'views', label: '阅读量', width: 100 },
      { prop: 'createTime', label: '创建时间', width: 160, type: 'datetime' },
      { prop: 'publishTime', label: '发布时间', width: 160, type: 'datetime' }
    ],
    actions: COMMON_ACTIONS.article,
    pagination: { show: true, pageSize: 20, pageSizes: [20, 50, 100] },
    border: true,
    stripe: true
  },
  
  // 商品表格预设
  product: {
    columns: [
      { prop: 'id', label: 'ID', width: 80 },
      { 
        prop: 'image', 
        label: '商品图片', 
        width: 100, 
        type: 'image',
        imageStyle: { width: '60px', height: '60px' }
      },
      { prop: 'name', label: '商品名称', minWidth: 200 },
      { prop: 'category', label: '分类', width: 120 },
      { prop: 'price', label: '价格', width: 120, type: 'currency' },
      { prop: 'stock', label: '库存', width: 100 },
      { 
        prop: 'status', 
        label: '状态', 
        width: 100, 
        type: 'tag',
        tagMap: COMMON_TAG_MAPS.status
      },
      { prop: 'createTime', label: '创建时间', width: 160, type: 'datetime' }
    ],
    actions: COMMON_ACTIONS.basic,
    pagination: { show: true, pageSize: 10, pageSizes: [10, 20, 50] },
    selection: true,
    border: true,
    stripe: true
  },
  
  // 简单列表预设
  simple: {
    columns: [
      { prop: 'id', label: 'ID', width: 80 },
      { prop: 'name', label: '名称', minWidth: 150 },
      { 
        prop: 'status', 
        label: '状态', 
        width: 100, 
        type: 'tag',
        tagMap: COMMON_TAG_MAPS.status
      },
      { prop: 'createTime', label: '创建时间', width: 160, type: 'datetime' }
    ],
    actions: COMMON_ACTIONS.basic,
    pagination: { show: true, pageSize: 10 },
    border: true,
    stripe: true
  }
}

/**
 * 获取预设配置
 * @param {string} presetName 预设名称
 * @param {object} customConfig 自定义配置（会覆盖预设配置）
 * @returns {object} 合并后的配置
 */
export function getTablePreset(presetName, customConfig = {}) {
  const preset = TABLE_PRESETS[presetName]
  if (!preset) {
    console.warn(`表格预设 "${presetName}" 不存在`)
    return customConfig
  }
  
  // 深度合并配置
  return mergeConfig(preset, customConfig)
}

/**
 * 深度合并配置对象
 * @param {object} target 目标对象
 * @param {object} source 源对象
 * @returns {object} 合并后的对象
 */
function mergeConfig(target, source) {
  const result = JSON.parse(JSON.stringify(target))
  
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (Array.isArray(source[key])) {
        result[key] = source[key]
      } else if (typeof source[key] === 'object' && source[key] !== null) {
        result[key] = mergeConfig(result[key] || {}, source[key])
      } else {
        result[key] = source[key]
      }
    }
  }
  
  return result
}

/**
 * 创建自定义标签映射
 * @param {object} mapping 标签映射对象
 * @returns {object} 标签映射
 */
export function createTagMap(mapping) {
  return mapping
}

/**
 * 创建自定义操作按钮
 * @param {array} actions 操作按钮数组
 * @returns {array} 操作按钮配置
 */
export function createActions(actions) {
  return actions
}
