<template>
  <div class="simple-table">
    <!-- 表格 -->
    <el-table 
      :data="data" 
      :loading="loading"
      stripe
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <!-- 选择列 -->
      <el-table-column 
        v-if="config.selection" 
        type="selection" 
        width="55" 
      />
      
      <!-- 动态列 -->
      <el-table-column
        v-for="column in processedColumns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :sortable="column.sortable"
      >
        <template #default="{ row, $index }">
          <!-- 自定义插槽 -->
          <slot
            v-if="column.type === 'slot'"
            :name="column.prop"
            :row="row"
            :index="$index"
            :value="row[column.prop]"
            :column="column"
          >
            <!-- 插槽默认内容 -->
            <span>{{ row[column.prop] }}</span>
          </slot>

          <!-- 图片 -->
          <el-image
            v-else-if="column.type === 'image'"
            :src="row[column.prop]"
            :style="column.imageStyle || { width: '40px', height: '40px' }"
            fit="cover"
          />

          <!-- 标签 -->
          <el-tag
            v-else-if="column.type === 'tag'"
            :type="getTagType(row[column.prop], column.tagMap)"
          >
            {{ getTagText(row[column.prop], column.tagMap) }}
          </el-tag>

          <!-- 货币 -->
          <span v-else-if="column.type === 'currency'">
            ¥{{ formatNumber(row[column.prop]) }}
          </span>

          <!-- 日期时间 -->
          <span v-else-if="column.type === 'datetime'">
            {{ formatDateTime(row[column.prop]) }}
          </span>

          <!-- 默认文本 -->
          <span v-else>{{ row[column.prop] }}</span>
        </template>
      </el-table-column>
      
      <!-- 操作列 -->
      <el-table-column 
        v-if="config.actions && config.actions.length > 0"
        label="操作" 
        :width="actionColumnWidth"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button
            v-for="action in config.actions"
            :key="action.type"
            :type="action.style"
            :size="action.size || 'small'"
            :icon="action.icon"
            @click="handleAction(action.type, row)"
          >
            {{ action.label }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <el-pagination
      v-if="config.pagination"
      :current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      style="margin-top: 20px; text-align: right;"
      @size-change="handleSizeChange"
      @current-change="handlePageChange"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // 数据源
  data: {
    type: Array,
    default: () => []
  },
  
  // 简化配置
  config: {
    type: Object,
    required: true,
    default: () => ({})
  },
  
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  
  // 分页信息
  total: {
    type: Number,
    default: 0
  },
  
  // 当前页
  currentPage: {
    type: Number,
    default: 1
  },
  
  // 页面大小
  pageSize: {
    type: Number,
    default: 10
  }
})

const emit = defineEmits([
  'action',
  'selection-change',
  'sort-change',
  'page-change',
  'size-change'
])

// 智能推断列类型和配置
const processedColumns = computed(() => {
  if (!props.config.columns) return []
  
  return props.config.columns.map(column => {
    if (typeof column === 'string') {
      // 字符串配置：自动推断
      return {
        prop: column,
        label: generateLabel(column),
        type: inferColumnType(column),
        ...getColumnDefaults(column)
      }
    } else {
      // 对象配置：补充默认值
      return {
        type: inferColumnType(column.prop),
        ...getColumnDefaults(column.prop),
        ...column
      }
    }
  })
})

// 操作列宽度
const actionColumnWidth = computed(() => {
  if (!props.config.actions) return 0
  return props.config.actions.length * 80 + 40
})

// 智能推断列类型
function inferColumnType(prop) {
  const patterns = {
    image: /^(avatar|image|img|photo|picture|pic)$/i,
    datetime: /^(time|date|created|updated|login|publish).*$/i,
    currency: /^(price|amount|money|cost|fee|total)$/i,
    tag: /^(status|state|type|category|role|level|grade)$/i
  }

  for (const [type, pattern] of Object.entries(patterns)) {
    if (pattern.test(prop)) return type
  }

  return 'text'
}

// 生成标题
function generateLabel(prop) {
  const labelMap = {
    id: 'ID',
    name: '名称',
    title: '标题',
    username: '用户名',
    email: '邮箱',
    phone: '手机号',
    avatar: '头像',
    image: '图片',
    status: '状态',
    role: '角色',
    createTime: '创建时间',
    updateTime: '更新时间',
    lastLoginTime: '最后登录',
    price: '价格',
    amount: '金额',
    category: '分类',
    description: '描述'
  }
  
  return labelMap[prop] || prop.charAt(0).toUpperCase() + prop.slice(1)
}

// 获取列默认配置
function getColumnDefaults(prop) {
  const type = inferColumnType(prop)
  const defaults = {
    image: { width: 80, imageStyle: { width: '40px', height: '40px' } },
    datetime: { width: 160 },
    currency: { width: 120 },
    tag: {
      width: 100,
      tagMap: getDefaultTagMap(prop)
    },
    slot: { width: 120 },
    text: {}
  }

  return defaults[type] || {}
}

// 获取默认标签映射
function getDefaultTagMap(prop) {
  const tagMaps = {
    status: {
      '1': { type: 'success', text: '启用' },
      '0': { type: 'danger', text: '禁用' },
      'active': { type: 'success', text: '活跃' },
      'inactive': { type: 'info', text: '非活跃' }
    },
    role: {
      'admin': { type: 'danger', text: '管理员' },
      'user': { type: 'primary', text: '普通用户' },
      'editor': { type: 'warning', text: '编辑员' }
    }
  }
  
  if (/^(status|state)$/i.test(prop)) return tagMaps.status
  if (/role/i.test(prop)) return tagMaps.role
  
  return tagMaps.status
}

// 获取标签类型
function getTagType(value, tagMap) {
  return tagMap?.[value]?.type || 'primary'
}

// 获取标签文本
function getTagText(value, tagMap) {
  return tagMap?.[value]?.text || value
}

// 格式化数字
function formatNumber(value) {
  if (!value && value !== 0) return '0.00'
  return Number(value).toFixed(2)
}

// 格式化日期时间
function formatDateTime(value) {
  if (!value) return ''
  return new Date(value).toLocaleString('zh-CN')
}



// 处理操作
function handleAction(type, row) {
  emit('action', { type, row })
}

// 处理选择变化
function handleSelectionChange(selection) {
  emit('selection-change', selection)
}

// 处理排序变化
function handleSortChange(sort) {
  emit('sort-change', sort)
}

// 处理页码变化
function handlePageChange(page) {
  emit('page-change', page)
}

// 处理页面大小变化
function handleSizeChange(size) {
  emit('size-change', size)
}
</script>

<style scoped>
.simple-table {
  width: 100%;
}

:deep(.el-table) {
  --el-table-border-color: #ebeef5;
  --el-table-text-color: #606266;
  --el-table-header-bg-color: #fafafa;
}

:deep(.el-button + .el-button) {
  margin-left: 8px;
}

/* 表格分页样式 */
:deep(.el-pagination) {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 0 20px 20px;
}

:deep(.el-pagination .el-select .el-input) {
  width: 120px;
}

:deep(.el-pagination .el-pagination__sizes) {
  margin-right: 10px;
}

:deep(.el-pagination .el-pagination__total) {
  margin-right: auto;
}
</style>
