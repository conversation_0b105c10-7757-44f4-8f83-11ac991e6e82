<template>
  <div class="simple-form">
    <el-form
      ref="formRef"
      :model="formData"
      :label-width="config.labelWidth || '120px'"
      :rules="formRules"
    >
      <!-- 动态表单字段 -->
      <el-form-item
        v-for="field in processedFields"
        :key="field.prop"
        :label="field.label"
        :prop="field.prop"
      >
        <!-- 输入框 -->
        <el-input
          v-if="field.type === 'input'"
          v-model="formData[field.prop]"
          :placeholder="field.placeholder"
          :disabled="field.disabled"
          clearable
        />
        
        <!-- 文本域 -->
        <el-input
          v-else-if="field.type === 'textarea'"
          v-model="formData[field.prop]"
          type="textarea"
          :rows="field.rows || 3"
          :placeholder="field.placeholder"
          :disabled="field.disabled"
        />
        
        <!-- 选择器 -->
        <el-select
          v-else-if="field.type === 'select'"
          v-model="formData[field.prop]"
          :placeholder="field.placeholder"
          :disabled="field.disabled"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="option in field.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        
        <!-- 单选框 -->
        <el-radio-group
          v-else-if="field.type === 'radio'"
          v-model="formData[field.prop]"
          :disabled="field.disabled"
        >
          <el-radio
            v-for="option in field.options"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </el-radio>
        </el-radio-group>
        
        <!-- 复选框 -->
        <el-checkbox-group
          v-else-if="field.type === 'checkbox'"
          v-model="formData[field.prop]"
          :disabled="field.disabled"
        >
          <el-checkbox
            v-for="option in field.options"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </el-checkbox>
        </el-checkbox-group>
        
        <!-- 开关 -->
        <el-switch
          v-else-if="field.type === 'switch'"
          v-model="formData[field.prop]"
          :disabled="field.disabled"
        />
        
        <!-- 日期选择 -->
        <el-date-picker
          v-else-if="field.type === 'date'"
          v-model="formData[field.prop]"
          type="date"
          :placeholder="field.placeholder"
          :disabled="field.disabled"
          style="width: 100%"
        />
        
        <!-- 日期时间选择 -->
        <el-date-picker
          v-else-if="field.type === 'datetime'"
          v-model="formData[field.prop]"
          type="datetime"
          :placeholder="field.placeholder"
          :disabled="field.disabled"
          format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
        
        <!-- 数字输入 -->
        <el-input-number
          v-else-if="field.type === 'number'"
          v-model="formData[field.prop]"
          :min="field.min"
          :max="field.max"
          :step="field.step || 1"
          :disabled="field.disabled"
          style="width: 100%"
        />
        
        <!-- 密码输入 -->
        <el-input
          v-else-if="field.type === 'password'"
          v-model="formData[field.prop]"
          type="password"
          :placeholder="field.placeholder"
          :disabled="field.disabled"
          show-password
          clearable
        />
        
        <!-- 级联选择器 -->
        <el-cascader
          v-else-if="field.type === 'cascader'"
          v-model="formData[field.prop]"
          :options="field.options"
          :props="field.cascaderProps || { checkStrictly: false }"
          :placeholder="field.placeholder"
          :disabled="field.disabled"
          style="width: 100%"
          @change="val => handleFieldChange(field.prop, val)"
        />
        
        <!-- 富文本编辑器 -->
        <RichEditor
          v-else-if="field.type === 'richtext'"
          v-model="formData[field.prop]"
          :placeholder="field.placeholder"
          :disabled="field.disabled"
          @change="val => handleFieldChange(field.prop, val)"
        />

        <!-- 文件上传 -->
        <el-upload
          v-else-if="field.type === 'upload'"
          :action="field.action || '#'"
          :accept="field.accept"
          :multiple="field.multiple"
          :limit="field.limit"
          :list-type="field.listType || 'text'"
          :file-list="Array.isArray(formData[field.prop]) ? formData[field.prop] : []"
          :auto-upload="field.action ? (field.autoUpload !== false) : false"
          :disabled="field.disabled"
          :on-success="(response, file, fileList) => handleUploadEvent(field, 'success', response, file, fileList)"
          :on-error="(error, file, fileList) => handleUploadEvent(field, 'error', error, file, fileList)"
          :on-remove="(file, fileList) => handleUploadEvent(field, 'remove', file, fileList)"
          :before-upload="file => handleUploadEvent(field, 'beforeUpload', file)"
          :on-preview="file => handleUploadEvent(field, 'preview', file)"
          :on-progress="(event, file, fileList) => handleUploadEvent(field, 'progress', event, file, fileList)"
          :on-change="(file, fileList) => handleUploadEvent(field, 'change', file, fileList)"
          :on-exceed="(files, fileList) => handleUploadEvent(field, 'exceed', files, fileList)"
        >
          <el-button v-if="field.listType !== 'picture-card'" type="primary">点击上传</el-button>
          <el-icon v-else><Plus /></el-icon>
        </el-upload>

        <!-- 日期范围选择器 -->
        <el-date-picker
          v-else-if="field.type === 'daterange'"
          v-model="formData[field.prop]"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :disabled="field.disabled"
          format="YYYY-MM-DD"
          style="width: 100%"
          @change="val => handleFieldChange(field.prop, val)"
        />
      </el-form-item>
      
      <!-- 操作按钮 -->
      <el-form-item v-if="config.actions">
        <el-button
          v-for="action in config.actions"
          :key="action.type"
          :type="action.style"
          :loading="action.loading"
          @click="handleAction(action.type)"
        >
          {{ action.label }}
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 图片预览弹窗 -->
    <ImagePreviewDialog
      v-model="previewDialogVisible"
      :image-url="previewImageUrl"
      @close="handlePreviewClose"
    />
  </div>
</template>

<script setup>
import { computed, ref, reactive, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import RichEditor from './RichEditor.vue'
import ImagePreviewDialog from './ImagePreviewDialog.vue'

const props = defineProps({
  // 表单配置
  config: {
    type: Object,
    required: true,
    default: () => ({})
  },
  
  // 初始数据
  initialData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['submit', 'reset', 'change', 'field-change'])

const formRef = ref()

// 表单数据
const formData = reactive({ ...props.initialData })

// 图片预览相关状态
const previewDialogVisible = ref(false)
const previewImageUrl = ref('')

// 监听初始数据变化
watch(() => props.initialData, (newData) => {
  Object.assign(formData, newData)
}, { deep: true })

// 智能处理字段配置
const processedFields = computed(() => {
  if (!props.config.fields) return []
  
  return props.config.fields.map(field => {
    if (typeof field === 'string') {
      // 字符串配置：自动推断
      return {
        prop: field,
        label: generateLabel(field),
        type: inferFieldType(field),
        ...getFieldDefaults(field)
      }
    } else {
      // 对象配置：补充默认值
      return {
        type: inferFieldType(field.prop),
        placeholder: generatePlaceholder(field.label || generateLabel(field.prop), field.type),
        ...getFieldDefaults(field.prop),
        ...field
      }
    }
  })
})

// 表单验证规则
const formRules = computed(() => {
  const rules = {}
  
  processedFields.value.forEach(field => {
    if (field.required) {
      rules[field.prop] = [
        { 
          required: true, 
          message: `请${getRequiredMessage(field)}`, 
          trigger: getValidateTrigger(field.type) 
        }
      ]
    }
    
    // 添加自定义规则
    if (field.rules) {
      rules[field.prop] = rules[field.prop] || []
      rules[field.prop].push(...field.rules)
    }
  })
  
  return rules
})

// 智能推断字段类型
function inferFieldType(prop) {
  const patterns = {
    password: /^(password|pwd|pass)$/i,
    email: /^(email|mail)$/i,
    phone: /^(phone|mobile|tel)$/i,
    number: /^(age|count|num|price|amount)$/i,
    textarea: /^(desc|description|content|remark|note)$/i,
    date: /^(date|time)$/i,
    datetime: /^(datetime|created|updated).*$/i,
    switch: /^(is|has|enable|disabled|active).*$/i,
    select: /^(type|category|status|role|level|grade)$/i
  }
  
  for (const [type, pattern] of Object.entries(patterns)) {
    if (pattern.test(prop)) return type
  }
  
  return 'input'
}

// 生成标题
function generateLabel(prop) {
  const labelMap = {
    id: 'ID',
    name: '名称',
    title: '标题',
    username: '用户名',
    email: '邮箱',
    phone: '手机号',
    password: '密码',
    age: '年龄',
    gender: '性别',
    status: '状态',
    role: '角色',
    createTime: '创建时间',
    updateTime: '更新时间',
    description: '描述',
    content: '内容',
    remark: '备注',
    category: '分类',
    type: '类型'
  }
  
  return labelMap[prop] || prop.charAt(0).toUpperCase() + prop.slice(1)
}

// 生成占位符
function generatePlaceholder(label, type) {
  const prefixMap = {
    input: '请输入',
    textarea: '请输入',
    password: '请输入',
    select: '请选择',
    date: '请选择',
    datetime: '请选择'
  }
  
  const prefix = prefixMap[type] || '请输入'
  return `${prefix}${label}`
}

// 获取字段默认配置
function getFieldDefaults(prop) {
  const type = inferFieldType(prop)
  const defaults = {
    select: {
      options: getDefaultOptions(prop)
    },
    radio: {
      options: getDefaultOptions(prop)
    },
    checkbox: {
      options: getDefaultOptions(prop)
    },
    number: {
      min: 0,
      max: 999999
    },
    textarea: {
      rows: 3
    }
  }
  
  return defaults[type] || {}
}

// 获取默认选项
function getDefaultOptions(prop) {
  const optionsMap = {
    status: [
      { label: '启用', value: '1' },
      { label: '禁用', value: '0' }
    ],
    gender: [
      { label: '男', value: 'male' },
      { label: '女', value: 'female' }
    ],
    role: [
      { label: '管理员', value: 'admin' },
      { label: '普通用户', value: 'user' },
      { label: '编辑员', value: 'editor' }
    ],
    type: [
      { label: '类型1', value: 'type1' },
      { label: '类型2', value: 'type2' }
    ]
  }
  
  return optionsMap[prop] || []
}

// 获取必填提示信息
function getRequiredMessage(field) {
  const messageMap = {
    input: `输入${field.label}`,
    textarea: `输入${field.label}`,
    password: `输入${field.label}`,
    select: `选择${field.label}`,
    radio: `选择${field.label}`,
    checkbox: `选择${field.label}`,
    date: `选择${field.label}`,
    datetime: `选择${field.label}`,
    switch: `设置${field.label}`,
    number: `输入${field.label}`
  }
  
  return messageMap[field.type] || `输入${field.label}`
}

// 获取验证触发方式
function getValidateTrigger(type) {
  const triggerMap = {
    input: 'blur',
    textarea: 'blur',
    password: 'blur',
    select: 'change',
    radio: 'change',
    checkbox: 'change',
    date: 'change',
    datetime: 'change',
    switch: 'change',
    number: 'blur'
  }
  
  return triggerMap[type] || 'blur'
}

// 处理操作
function handleAction(type) {
  if (type === 'submit') {
    handleSubmit()
  } else if (type === 'reset') {
    handleReset()
  }
}

// 处理提交
async function handleSubmit() {
  try {
    await formRef.value?.validate()
    emit('submit', { ...formData })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 处理重置
function handleReset() {
  formRef.value?.resetFields()
  Object.assign(formData, props.initialData)
  emit('reset')
}

// 处理字段变化
function handleFieldChange(prop, value) {
  emit('field-change', prop, value)
  emit('change', { ...formData })
}

// 统一的上传事件处理函数
function handleUploadEvent(field, eventType, ...args) {
  console.log(`[SimpleForm] Upload event: ${eventType} for ${field.prop}`, args)

  // 处理预览事件
  if (eventType === 'preview') {
    const file = args[0]
    if (file && file.url) {
      previewImageUrl.value = file.url
      previewDialogVisible.value = true
      return
    }
  }

  // 处理超出限制事件
  if (eventType === 'exceed') {
    const files = args[0]
    const fileList = args[1]
    console.log(`[SimpleForm] Exceed limit for ${field.prop}:`, files, fileList, `limit: ${field.limit}`)

    // 调用统一的事件处理器
    if (field && typeof field.onEvent === 'function') {
      field.onEvent(eventType, field.prop, files, fileList)
    }
    return
  }

  // 处理无 action 的本地文件上传
  if (eventType === 'change' && !field.action) {
    const file = args[0]
    const fileList = args[1] || []

    console.log(`[SimpleForm] Local file change for ${field.prop}:`, file, fileList)

    if (file && file.raw && file.status !== 'removed') {
      // 转换为 base64 或 blob URL
      const reader = new FileReader()
      reader.onload = (e) => {
        const newFile = {
          ...file,
          url: e.target.result,
          status: 'success'
        }

        // 更新文件列表
        const updatedFileList = fileList.map(f => f.uid === file.uid ? newFile : f)
        console.log(`[SimpleForm] Updated local fileList for ${field.prop}:`, updatedFileList)

        formData[field.prop] = updatedFileList
        emit('field-change', field.prop, updatedFileList)
        emit('change', { ...formData })
      }
      reader.readAsDataURL(file.raw)
    } else {
      // 文件删除或其他情况
      console.log(`[SimpleForm] Direct update fileList for ${field.prop}:`, fileList)
      formData[field.prop] = fileList
      emit('field-change', field.prop, fileList)
      emit('change', { ...formData })
    }
  }

  // 处理有 action 的正常上传事件
  else if (eventType === 'success' || eventType === 'change' || eventType === 'remove') {
    let fileList = []

    if (eventType === 'success') {
      fileList = args[2] || []
    } else if (eventType === 'change') {
      fileList = args[1] || []
    } else if (eventType === 'remove') {
      fileList = args[1] || []
    }

    console.log(`[SimpleForm] ${eventType} event for ${field.prop}, updating fileList:`, fileList)

    formData[field.prop] = fileList
    emit('field-change', field.prop, fileList)
    emit('change', { ...formData })
  }

  // 调用统一的事件处理器
  if (field && typeof field.onEvent === 'function') {
    console.log(`[SimpleForm] Calling unified onEvent for ${field.prop}`)
    const result = field.onEvent(eventType, field.prop, ...args)

    // 对于 beforeUpload 事件，需要返回结果
    if (eventType === 'beforeUpload') {
      return result
    }
  }

  // 兼容旧的单独事件回调
  const eventCallbacks = {
    success: field.onSuccess,
    error: field.onError,
    remove: field.onRemove,
    beforeUpload: field.beforeUpload,
    preview: field.onPreview,
    progress: field.onProgress,
    change: field.onChange,
    exceed: field.onExceed
  }

  const callback = eventCallbacks[eventType]
  if (typeof callback === 'function') {
    console.log(`[SimpleForm] Calling legacy ${eventType} callback for ${field.prop}`)
    const result = callback(...args)
    if (eventType === 'beforeUpload') {
      return result
    }
  }

  return true
}

// 处理预览弹窗关闭
function handlePreviewClose() {
  previewDialogVisible.value = false
  previewImageUrl.value = ''
}





// 监听表单数据变化
watch(formData, (newData) => {
  emit('change', { ...newData })
}, { deep: true })

// 暴露方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields(),
  clearValidate: () => formRef.value?.clearValidate(),
  getFormData: () => ({ ...formData }),
  setFormData: (data) => Object.assign(formData, data)
})
</script>

<style scoped>
.simple-form {
  width: 100%;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-form-item) {
  margin-bottom: 22px;
}

:deep(.el-button + .el-button) {
  margin-left: 12px;
}
</style>
