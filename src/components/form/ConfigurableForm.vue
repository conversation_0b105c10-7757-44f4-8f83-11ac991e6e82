<template>
  <el-form
    ref="formRef"
    :model="model"
    :label-width="config.labelWidth || '100px'"
    :rules="formRules"
    @submit.prevent="handleSubmit"
  >
    <!-- 动态渲染表单字段 -->
    <el-form-item
      v-for="field in config.fields"
      :key="field.prop"
      :label="field.label"
      :prop="field.prop"
    >
      <!-- 输入框 -->
      <el-input
        v-if="field.type === 'input'"
        v-model="model[field.prop]"
        :placeholder="field.placeholder"
        :disabled="field.disabled"
        @change="handleFieldChange(field.prop, model[field.prop])"
      />

      <!-- 文本域 -->
      <el-input
        v-else-if="field.type === 'textarea'"
        v-model="model[field.prop]"
        type="textarea"
        :rows="field.rows || 3"
        :placeholder="field.placeholder"
        :disabled="field.disabled"
        @change="handleFieldChange(field.prop, model[field.prop])"
      />

      <!-- 选择器 -->
      <el-select
        v-else-if="field.type === 'select'"
        v-model="model[field.prop]"
        :placeholder="field.placeholder"
        :disabled="field.disabled"
        style="width: 100%"
        @change="handleFieldChange(field.prop, model[field.prop])"
      >
        <el-option
          v-for="option in field.options"
          :key="option.value"
          :label="option.label"
          :value="option.value"
        />
      </el-select>

      <!-- 单选框 -->
      <el-radio-group
        v-else-if="field.type === 'radio'"
        v-model="model[field.prop]"
        :disabled="field.disabled"
        @change="handleFieldChange(field.prop, model[field.prop])"
      >
        <el-radio
          v-for="option in field.options"
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </el-radio>
      </el-radio-group>

      <!-- 复选框 -->
      <el-checkbox-group
        v-else-if="field.type === 'checkbox'"
        v-model="model[field.prop]"
        :disabled="field.disabled"
        @change="handleFieldChange(field.prop, model[field.prop])"
      >
        <el-checkbox
          v-for="option in field.options"
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </el-checkbox>
      </el-checkbox-group>

      <!-- 日期选择器 -->
      <el-date-picker
        v-else-if="field.type === 'date'"
        v-model="model[field.prop]"
        type="date"
        :placeholder="field.placeholder"
        :disabled="field.disabled"
        style="width: 100%"
        @change="handleFieldChange(field.prop, model[field.prop])"
      />

      <!-- 开关 -->
      <el-switch
        v-else-if="field.type === 'switch'"
        v-model="model[field.prop]"
        :disabled="field.disabled"
        @change="handleFieldChange(field.prop, model[field.prop])"
      />

      <!-- 日期时间选择器 -->
      <el-date-picker
        v-else-if="field.type === 'datetime'"
        v-model="model[field.prop]"
        type="datetime"
        :placeholder="field.placeholder"
        :disabled="field.disabled"
        format="YYYY-MM-DD HH:mm:ss"
        style="width: 100%"
        @change="handleFieldChange(field.prop, model[field.prop])"
      />

      <!-- 日期范围选择器 -->
      <el-date-picker
        v-else-if="field.type === 'daterange'"
        v-model="model[field.prop]"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :disabled="field.disabled"
        format="YYYY-MM-DD"
        style="width: 100%"
        @change="handleFieldChange(field.prop, model[field.prop])"
      />

      <!-- 级联选择器 -->
      <el-cascader
        v-else-if="field.type === 'cascader'"
        v-model="model[field.prop]"
        :options="field.options"
        :props="field.cascaderProps || { checkStrictly: false }"
        :placeholder="field.placeholder"
        :disabled="field.disabled"
        style="width: 100%"
        @change="handleFieldChange(field.prop, model[field.prop])"
      />

      <!-- 富文本编辑器 -->
      <RichEditor
        v-else-if="field.type === 'richtext'"
        v-model="model[field.prop]"
        :placeholder="field.placeholder"
        :disabled="field.disabled"
        @change="handleFieldChange(field.prop, model[field.prop])"
      />

      <!-- 文件上传 -->
      <el-upload
        v-else-if="field.type === 'upload'"
        :action="field.action || '#'"
        :accept="field.accept"
        :multiple="field.multiple"
        :limit="field.limit"
        :list-type="field.listType || 'text'"
        :file-list="Array.isArray(model[field.prop]) ? model[field.prop] : []"
        :auto-upload="false"
        :disabled="field.disabled"
        @change="handleUploadChange(field.prop, $event)"
      >
        <el-button type="primary">点击上传</el-button>
      </el-upload>
    </el-form-item>

    <!-- 操作按钮 -->
    <el-form-item v-if="config.actions">
      <el-button
        v-for="action in config.actions"
        :key="action.type"
        :type="action.style"
        @click="handleAction(action.type)"
      >
        {{ action.label }}
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from 'vue'
import RichEditor from './RichEditor.vue'

const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  model: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['submit', 'reset', 'field-change'])

const formRef = ref()

// 构建表单验证规则
const formRules = computed(() => {
  const rules = {}
  props.config.fields?.forEach(field => {
    if (field.rules) {
      rules[field.prop] = field.rules
    }
  })
  return rules
})

// 处理字段变化
const handleFieldChange = (prop, value) => {
  emit('field-change', prop, value)
}

// 处理文件上传变化
const handleUploadChange = (prop, fileList) => {
  props.model[prop] = fileList
  emit('field-change', prop, fileList)
}

// 处理操作按钮点击
const handleAction = async (type) => {
  if (type === 'submit') {
    await handleSubmit()
  } else if (type === 'reset') {
    handleReset()
  }
}

// 处理表单提交
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    emit('submit', { ...props.model })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 处理表单重置
const handleReset = () => {
  formRef.value.resetFields()
  emit('reset')
}

// 暴露方法
defineExpose({
  validate: () => formRef.value.validate(),
  resetFields: () => formRef.value.resetFields()
})
</script>

<style scoped>
:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-upload__tip) {
  color: #909399;
  font-size: 12px;
}
</style>
