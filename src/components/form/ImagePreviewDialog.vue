<template>
  <el-dialog
    v-model="visible"
    title="图片预览"
    width="80%"
    :before-close="handleClose"
    class="image-preview-dialog"
  >
    <div class="preview-container">
      <div class="image-wrapper" @wheel="handleWheel">
        <img
          ref="imageRef"
          :src="imageUrl"
          :style="imageStyle"
          @load="handleImageLoad"
          @error="handleImageError"
        />
      </div>
      
      <!-- 控制按钮 -->
      <div class="control-buttons">
        <el-button-group>
          <el-button @click="zoomIn" :icon="ZoomIn" circle />
          <el-button @click="zoomOut" :icon="ZoomOut" circle />
          <el-button @click="resetZoom" :icon="Refresh" circle />
          <el-button @click="rotateLeft" :icon="RefreshLeft" circle />
          <el-button @click="rotateRight" :icon="RefreshRight" circle />
        </el-button-group>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { 
  ZoomIn, 
  ZoomOut, 
  Refresh, 
  RefreshLeft, 
  RefreshRight 
} from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  imageUrl: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'close'])

const imageRef = ref()
const scale = ref(1)
const rotation = ref(0)
const imageLoaded = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const imageStyle = computed(() => ({
  transform: `scale(${scale.value}) rotate(${rotation.value}deg)`,
  transition: 'transform 0.3s ease',
  maxWidth: '100%',
  maxHeight: '70vh',
  cursor: 'grab'
}))

// 监听弹窗打开，重置状态
watch(visible, (newVal) => {
  if (newVal) {
    resetZoom()
  }
})

// 放大
const zoomIn = () => {
  if (scale.value < 3) {
    scale.value += 0.2
  }
}

// 缩小
const zoomOut = () => {
  if (scale.value > 0.2) {
    scale.value -= 0.2
  }
}

// 重置缩放
const resetZoom = () => {
  scale.value = 1
  rotation.value = 0
}

// 左旋转
const rotateLeft = () => {
  rotation.value -= 90
}

// 右旋转
const rotateRight = () => {
  rotation.value += 90
}

// 鼠标滚轮缩放
const handleWheel = (event) => {
  event.preventDefault()
  if (event.deltaY < 0) {
    zoomIn()
  } else {
    zoomOut()
  }
}

// 图片加载完成
const handleImageLoad = () => {
  imageLoaded.value = true
}

// 图片加载错误
const handleImageError = () => {
  console.error('图片加载失败:', props.imageUrl)
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  emit('close')
}
</script>

<style scoped>
.image-preview-dialog {
  .preview-container {
    position: relative;
    text-align: center;
    min-height: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  .image-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-bottom: 20px;
  }
  
  .control-buttons {
    position: sticky;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 8px;
    backdrop-filter: blur(5px);
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>
