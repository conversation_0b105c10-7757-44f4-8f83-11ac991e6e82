<template>
  <div class="rich-editor">
    <Toolbar 
      style="border-bottom: 1px solid #ccc" 
      :editor="editorRef" 
      :defaultConfig="toolbarConfigMerged" 
      :mode="mode" 
    />
    <Editor 
      style="height: 300px; overflow-y: hidden;" 
      v-model="valueHtml" 
      :defaultConfig="editorConfigMerged" 
      :mode="mode" 
      @onCreated="handleCreated" 
      @onChange="handleChange"
    />
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, onBeforeUnmount, watch } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import '@wangeditor/editor/dist/css/style.css'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入内容...'
  },
  height: {
    type: [String, Number],
    default: 300
  },
  disabled: {
    type: Boolean,
    default: false
  },
  toolbarConfig: {
    type: Object,
    default: () => ({})
  },
  editorConfig: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 编辑器实例，必须用 shallowRef
const editorRef = ref()

// 内容 HTML
const valueHtml = ref(props.modelValue)

// 模式
const mode = 'default' // 或 'simple'

// 默认工具栏配置
const defaultToolbarConfig = {
  excludeKeys: [
    'group-video', // 排除视频
    'fullScreen' // 排除全屏
  ]
}

// 默认编辑器配置
const defaultEditorConfig = {
  placeholder: props.placeholder,
  readOnly: props.disabled,
  MENU_CONF: {
    // 配置上传图片
    uploadImage: {
      server: '/api/upload/image',
      fieldName: 'file',
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedFileTypes: ['image/*'],
      customUpload(file, insertFn) {
        // 自定义上传逻辑
        console.log('上传文件:', file)
        // 这里可以调用你的上传接口
        // 示例：模拟上传成功
        setTimeout(() => {
          const url = URL.createObjectURL(file)
          insertFn(url, file.name, url)
        }, 1000)
      }
    }
  }
}

// 合并配置
const toolbarConfigMerged = { ...defaultToolbarConfig, ...props.toolbarConfig }
const editorConfigMerged = { ...defaultEditorConfig, ...props.editorConfig }

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})

// 编辑器创建完毕时的回调函数
const handleCreated = (editor) => {
  editorRef.value = editor // 记录 editor 实例，重要！
}

// 内容变化时的回调函数
const handleChange = (editor) => {
  const html = editor.getHtml()
  emit('update:modelValue', html)
  emit('change', html)
}

// 监听外部传入的值变化
watch(() => props.modelValue, (newVal) => {
  if (newVal !== valueHtml.value) {
    valueHtml.value = newVal
  }
})

// 暴露编辑器实例方法
defineExpose({
  getEditor: () => editorRef.value,
  getHtml: () => editorRef.value?.getHtml() || '',
  getText: () => editorRef.value?.getText() || '',
  clear: () => editorRef.value?.clear(),
  focus: () => editorRef.value?.focus(),
  blur: () => editorRef.value?.blur()
})
</script>

<style scoped>
.rich-editor {
  border: 1px solid #ccc;
  border-radius: 4px;
  overflow: hidden;
}

:deep(.w-e-text-placeholder) {
  color: #c0c4cc;
  font-style: normal;
}

:deep(.w-e-text-container) {
  background-color: #fff;
}

:deep(.w-e-toolbar) {
  background-color: #fafafa;
}
</style>
