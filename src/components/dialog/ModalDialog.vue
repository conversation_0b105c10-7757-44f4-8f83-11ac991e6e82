<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="width"
    :fullscreen="fullscreen"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="showClose"
    :draggable="draggable"
    :destroy-on-close="destroyOnClose"
    @open="handleOpen"
    @close="handleClose"
    @closed="handleClosed"
  >
    <!-- 弹窗内容 -->
    <div class="modal-content" :style="{ height: contentHeight }">
      <!-- 纯文本内容 -->
      <div v-if="type === 'text'" class="text-content">
        {{ content }}
      </div>

      <!-- HTML 内容 -->
      <div v-else-if="type === 'html'" class="html-content" v-html="content"></div>

      <!-- URL 链接内容 (iframe) -->
      <iframe
        v-else-if="type === 'url'"
        :src="content"
        class="iframe-content"
        :style="{ height: contentHeight }"
        frameborder="0"
        @load="handleIframeLoad"
      ></iframe>

      <!-- 组件内容 -->
      <component v-else-if="type === 'component'" :is="content" v-bind="componentProps" />

      <!-- 图片内容 -->
      <div v-else-if="type === 'image'" class="image-content">
        <el-image
          :src="content"
          :preview-src-list="[content]"
          fit="contain"
          style="width: 100%; height: 100%;"
        />
      </div>

      <!-- 视频内容 -->
      <div v-else-if="type === 'video'" class="video-content">
        <video
          :src="content"
          controls
          style="width: 100%; height: 100%;"
        >
          您的浏览器不支持视频播放。
        </video>
      </div>

      <!-- 默认插槽内容 -->
      <div v-else class="slot-content">
        <slot></slot>
      </div>
    </div>

    <!-- 自定义底部 -->
    <template #footer v-if="showFooter">
      <div class="dialog-footer">
        <slot name="footer">
          <el-button @click="handleCancel">{{ cancelText }}</el-button>
          <el-button 
            v-if="showConfirm" 
            type="primary" 
            @click="handleConfirm"
            :loading="confirmLoading"
          >
            {{ confirmText }}
          </el-button>
        </slot>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits } from 'vue'

const props = defineProps({
  // 基础属性
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '弹窗标题'
  },
  width: {
    type: [String, Number],
    default: '50%'
  },
  fullscreen: {
    type: Boolean,
    default: false
  },
  
  // 内容相关
  type: {
    type: String,
    default: 'slot', // text, html, url, component, image, video, slot
    validator: (value) => {
      return ['text', 'html', 'url', 'component', 'image', 'video', 'slot'].includes(value)
    }
  },
  content: {
    type: [String, Object],
    default: ''
  },
  contentHeight: {
    type: String,
    default: 'auto'
  },
  componentProps: {
    type: Object,
    default: () => ({})
  },
  
  // 交互属性
  closeOnClickModal: {
    type: Boolean,
    default: true
  },
  closeOnPressEscape: {
    type: Boolean,
    default: true
  },
  showClose: {
    type: Boolean,
    default: true
  },
  draggable: {
    type: Boolean,
    default: false
  },
  destroyOnClose: {
    type: Boolean,
    default: false
  },
  
  // 底部按钮
  showFooter: {
    type: Boolean,
    default: true
  },
  showConfirm: {
    type: Boolean,
    default: true
  },
  confirmText: {
    type: String,
    default: '确定'
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  confirmLoading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'update:modelValue',
  'open',
  'close',
  'closed',
  'confirm',
  'cancel',
  'iframe-load'
])

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 事件处理
const handleOpen = () => {
  emit('open')
}

const handleClose = () => {
  emit('close')
}

const handleClosed = () => {
  emit('closed')
}

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  visible.value = false
  emit('cancel')
}

const handleIframeLoad = () => {
  emit('iframe-load')
}

// 暴露方法
defineExpose({
  open: () => {
    visible.value = true
  },
  close: () => {
    visible.value = false
  }
})
</script>

<style scoped>
.modal-content {
  width: 100%;
  overflow: auto;
}

.text-content {
  padding: 20px;
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-word;
}

.html-content {
  padding: 20px;
  line-height: 1.6;
}

.iframe-content {
  width: 100%;
  border: none;
  background: #fff;
}

.image-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.video-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background: #000;
}

.slot-content {
  padding: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* HTML 内容样式 */
:deep(.html-content h1),
:deep(.html-content h2),
:deep(.html-content h3),
:deep(.html-content h4),
:deep(.html-content h5),
:deep(.html-content h6) {
  color: #303133;
  margin: 16px 0 8px 0;
}

:deep(.html-content p) {
  margin: 8px 0;
  color: #606266;
}

:deep(.html-content a) {
  color: #409eff;
  text-decoration: none;
}

:deep(.html-content a:hover) {
  text-decoration: underline;
}

:deep(.html-content code) {
  background: #f5f7fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

:deep(.html-content pre) {
  background: #f5f7fa;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
}

:deep(.html-content blockquote) {
  border-left: 4px solid #dcdfe6;
  padding-left: 16px;
  margin: 16px 0;
  color: #909399;
}

:deep(.html-content img) {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

:deep(.html-content table) {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

:deep(.html-content th),
:deep(.html-content td) {
  border: 1px solid #dcdfe6;
  padding: 8px 12px;
  text-align: left;
}

:deep(.html-content th) {
  background: #f5f7fa;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .text-content,
  .html-content,
  .slot-content {
    padding: 15px;
  }
  
  .iframe-content {
    min-height: 300px;
  }
}
</style>
