import './assets/main.css'

import { createApp } from 'vue'
import App from './App.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import router from './router'
import { checkPermissionDirective } from './utils/permission'

const app = createApp(App)

// 注册 Element Plus
app.use(ElementPlus)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册权限指令
app.directive('permission', {
  mounted(el, binding) {
    console.log('权限指令 mounted:', binding.value)
    const hasPermission = checkPermissionDirective(binding.value)
    console.log('权限检查结果:', hasPermission)
    if (!hasPermission) {
      el.style.display = 'none'
    }
  },
  updated(el, binding) {
    console.log('权限指令 updated:', binding.value)
    const hasPermission = checkPermissionDirective(binding.value)
    console.log('权限检查结果:', hasPermission)
    if (!hasPermission) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
})

// 使用路由
app.use(router)

app.mount('#app')
