/**
 * 菜单配置文件
 * 控制菜单的显示模式和行为
 */

export const menuConfig = {
  // 菜单模式配置
  mode: {
    // 是否使用扁平化菜单（不折叠）
    flatMenu: true,

    // 是否支持3级菜单
    supportThreeLevel: true,

    // 默认展开的菜单项
    defaultOpeneds: [],

    // 是否只保持一个子菜单的展开
    uniqueOpened: false
  },
  
  // 菜单样式配置
  style: {
    // 菜单背景色
    backgroundColor: '#001529',
    
    // 文字颜色
    textColor: '#fff',
    
    // 激活状态文字颜色
    activeTextColor: '#1890ff',
    
    // 是否显示图标
    showIcon: true
  },
  
  // 权限配置
  permission: {
    // 是否启用权限控制
    enabled: true,
    
    // 无权限时是否隐藏菜单项
    hideOnNoPermission: true
  }
}

/**
 * 获取菜单配置
 * @returns {Object} 菜单配置对象
 */
export function getMenuConfig() {
  return menuConfig
}

/**
 * 设置菜单模式
 * @param {string} mode - 菜单模式 ('flat' | 'hierarchical')
 */
export function setMenuMode(mode) {
  switch (mode) {
    case 'flat':
      menuConfig.mode.flatMenu = true
      menuConfig.mode.supportThreeLevel = false
      break
    case 'hierarchical':
      menuConfig.mode.flatMenu = false
      menuConfig.mode.supportThreeLevel = true
      break
    default:
      console.warn('未知的菜单模式:', mode)
  }
}

/**
 * 切换扁平化菜单
 * @param {boolean} enabled - 是否启用扁平化菜单
 */
export function toggleFlatMenu(enabled = true) {
  menuConfig.mode.flatMenu = enabled
  if (enabled) {
    menuConfig.mode.supportThreeLevel = false
  }
}

/**
 * 切换3级菜单支持
 * @param {boolean} enabled - 是否支持3级菜单
 */
export function toggleThreeLevelMenu(enabled = true) {
  menuConfig.mode.supportThreeLevel = enabled
  if (enabled) {
    menuConfig.mode.flatMenu = false
  }
}

export default menuConfig
