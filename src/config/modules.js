/**
 * 模块显示配置
 * 用于控制各个模块在菜单中的显示状态
 */

// 模块显示配置 - 集中管理所有模块的配置信息
export const moduleConfig = {
  // 控制台模块
  dashboard: {
    title: '控制台',
    icon: 'Monitor',
    visible: true,
    order: 1,
    permission: 'dashboard:view',
    description: '系统首页和数据概览'
  },

  // 用户管理模块
  user: {
    title: '用户管理',
    icon: 'User',
    visible: true,
    order: 3,
    permission: 'user:view',
    description: '用户信息管理和权限控制'
  },

  // 组件管理模块
  demo: {
    title: '组件管理',
    icon: 'Management',
    visible: false,
    order: 3,
    permission: ['form:manage', 'table:manage'],
    description: '表单、表格等组件演示'
  },

  // 商品管理模块
  product: {
    title: '商品管理',
    icon: 'Goods',
    visible: true,
    order: 4,
    permission: 'product:view',
    description: '商品信息和库存管理'
  },

  // 订单管理模块
  order: {
    title: '订单管理',
    icon: 'ShoppingCart',
    visible: true,
    order: 5,
    permission: 'order:view',
    description: '订单处理和状态管理'
  },

  // 配置管理模块
  config: {
    title: '配置管理',
    icon: 'Setting',
    visible: true,
    order: 6,
    permission: 'config:view',
    description: '系统配置和参数设置'
  },

  // 后台用户管理模块
  adminUser: {
    title: '管理员',
    icon: 'UserFilled',
    visible: true,
    order: 2,
    permission: 'admin_user:view',
    description: '后台用户和角色管理'
  },

  // 系统管理模块
  system: {
    title: '系统管理',
    icon: 'Tools',
    visible: false,
    order: 8,
    permission: 'system:view',
    description: '系统监控和日志管理'
  },

  // 高级演示模块
  advancedDemo: {
    title: '高级演示',
    icon: 'Operation',
    visible: false,
    order: 10,
    permission: 'advanced:view',
    description: '演示3级菜单结构和高级功能'
  }
}

// 获取模块配置
export function getModuleConfig(moduleName) {
  return moduleConfig[moduleName] || { visible: true, order: 999 }
}

// 获取模块的menuGroup配置
export function getModuleMenuGroup(moduleName) {
  const config = moduleConfig[moduleName]
  if (!config) return null

  return {
    title: config.title,
    icon: config.icon,
    permission: config.permission,
    order: config.order,
    visible: config.visible,
    description: config.description
  }
}

// 获取所有可见模块
export function getVisibleModules() {
  return Object.entries(moduleConfig)
    .filter(([_, config]) => config.visible)
    .sort(([_, a], [__, b]) => a.order - b.order)
    .map(([name, config]) => ({ name, ...config }))
}

// 设置模块可见性
export function setModuleVisibility(moduleName, visible) {
  if (moduleConfig[moduleName]) {
    moduleConfig[moduleName].visible = visible
    return true
  }
  return false
}

// 批量设置模块可见性
export function setMultipleModuleVisibility(settings) {
  const results = {}
  Object.entries(settings).forEach(([moduleName, visible]) => {
    results[moduleName] = setModuleVisibility(moduleName, visible)
  })
  return results
}

// 重置所有模块为可见
export function resetAllModulesVisible() {
  Object.keys(moduleConfig).forEach(moduleName => {
    moduleConfig[moduleName].visible = true
  })
}

// 获取模块统计信息
export function getModuleStats() {
  const total = Object.keys(moduleConfig).length
  const visible = Object.values(moduleConfig).filter(config => config.visible).length
  const hidden = total - visible
  
  return {
    total,
    visible,
    hidden,
    visibilityRate: Math.round((visible / total) * 100)
  }
}

// 导出默认配置
export default moduleConfig
