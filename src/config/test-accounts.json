{"testAccounts": [{"role": "superadmin", "username": "superadmin", "password": "123456", "label": "超级管理员", "description": "拥有系统所有权限，包括系统核心配置", "color": "#ff4d4f", "features": ["所有权限", "系统配置", "用户管理", "数据备份"]}, {"role": "admin", "username": "admin", "password": "123456", "label": "管理员", "description": "拥有大部分管理权限，负责日常管理工作", "color": "#fa8c16", "features": ["用户管理", "角色管理", "内容管理", "系统监控"]}, {"role": "editor", "username": "editor", "password": "123456", "label": "编辑员", "description": "拥有内容编辑和用户管理权限", "color": "#1890ff", "features": ["内容编辑", "用户查看", "表单管理", "文件上传"]}, {"role": "content_manager", "username": "content", "password": "123456", "label": "内容管理员", "description": "专门负责内容管理相关工作", "color": "#52c41a", "features": ["内容管理", "文件管理", "内容发布", "数据查看"]}, {"role": "user", "username": "user", "password": "123456", "label": "普通用户", "description": "基本的查看权限，适合一般员工", "color": "#722ed1", "features": ["数据查看", "文件下载", "基本操作", "统计查看"]}, {"role": "guest", "username": "guest", "password": "123456", "label": "访客", "description": "最基本的访问权限", "color": "#8c8c8c", "features": ["控制台查看", "基础统计", "只读访问"]}], "quickLoginOptions": {"showQuickLogin": true, "autoFillCredentials": true, "showAccountFeatures": true, "defaultRole": "admin"}, "developmentSettings": {"autoLogin": false, "skipValidation": false, "mockApiCalls": true, "debugMode": true}}