/**
 * Cookie 操作工具函数
 * 提供 cookie 的读取、设置、删除等功能
 */

/**
 * 获取指定名称的 cookie 值
 * @param {string} name cookie 名称
 * @returns {string|null} cookie 值，如果不存在返回 null
 */
export function getCookie(name) {
  const value = `; ${document.cookie}`
  const parts = value.split(`; ${name}=`)
  if (parts.length === 2) {
    return parts.pop().split(';').shift()
  }
  return null
}

/**
 * 设置 cookie
 * @param {string} name cookie 名称
 * @param {string} value cookie 值
 * @param {Object} options 选项
 * @param {number} options.days 过期天数
 * @param {string} options.path 路径
 * @param {string} options.domain 域名
 * @param {boolean} options.secure 是否只在 HTTPS 下传输
 * @param {string} options.sameSite SameSite 属性
 */
export function setCookie(name, value, options = {}) {
  const {
    days = 7,
    path = '/',
    domain = '',
    secure = false,
    sameSite = 'Lax'
  } = options

  let cookieString = `${name}=${value}`

  if (days) {
    const date = new Date()
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000))
    cookieString += `; expires=${date.toUTCString()}`
  }

  cookieString += `; path=${path}`

  if (domain) {
    cookieString += `; domain=${domain}`
  }

  if (secure) {
    cookieString += '; secure'
  }

  cookieString += `; samesite=${sameSite}`

  document.cookie = cookieString
}

/**
 * 删除指定名称的 cookie
 * @param {string} name cookie 名称
 * @param {Object} options 选项
 * @param {string} options.path 路径
 * @param {string} options.domain 域名
 */
export function removeCookie(name, options = {}) {
  const { path = '/', domain = '' } = options
  
  let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}`
  
  if (domain) {
    cookieString += `; domain=${domain}`
  }
  
  document.cookie = cookieString
}

/**
 * 获取所有 cookie
 * @returns {Object} 包含所有 cookie 的对象
 */
export function getAllCookies() {
  const cookies = {}
  if (document.cookie) {
    document.cookie.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=')
      if (name && value) {
        cookies[name] = decodeURIComponent(value)
      }
    })
  }
  return cookies
}

/**
 * 清除所有可访问的 cookie
 * 注意：无法清除 HttpOnly cookie
 */
export function clearAllCookies() {
  const cookies = getAllCookies()
  const paths = ['/', '/admin', '/api', '/auth']
  
  Object.keys(cookies).forEach(name => {
    paths.forEach(path => {
      removeCookie(name, { path })
    })
  })
}

/**
 * 清除认证相关的 cookie
 * @param {Array} authCookieNames 认证相关的 cookie 名称列表
 */
export function clearAuthCookies(authCookieNames = ['auth', 'token', 'session', 'jwt']) {
  const cookies = getAllCookies()
  const paths = ['/', '/admin', '/api', '/auth']
  
  console.log('当前所有cookie:', cookies)
  
  Object.keys(cookies).forEach(name => {
    const shouldClear = authCookieNames.some(authName => 
      name.toLowerCase().includes(authName.toLowerCase())
    )
    
    if (shouldClear) {
      console.log(`清除认证相关cookie: ${name}`)
      paths.forEach(path => {
        removeCookie(name, { path })
      })
    }
  })
}

/**
 * 检查是否有认证相关的 cookie
 * @param {Array} authCookieNames 认证相关的 cookie 名称列表
 * @returns {boolean} 是否存在认证相关的 cookie
 */
export function hasAuthCookies(authCookieNames = ['auth', 'token', 'session', 'jwt']) {
  const cookies = getAllCookies()
  
  return Object.keys(cookies).some(name => 
    authCookieNames.some(authName => 
      name.toLowerCase().includes(authName.toLowerCase())
    )
  )
}

/**
 * 调试 cookie 信息
 */
export function debugCookies() {
  console.group('🍪 Cookie 调试信息')
  console.log('document.cookie:', document.cookie)
  console.log('解析后的cookies:', getAllCookies())
  console.log('是否有认证相关cookie:', hasAuthCookies())
  console.groupEnd()
}
