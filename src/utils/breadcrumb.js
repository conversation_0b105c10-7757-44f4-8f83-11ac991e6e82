import { menuConfig } from '@/router/index.js'

/**
 * 面包屑生成工具函数
 * 基于路由和菜单配置自动生成面包屑导航
 */

/**
 * 查找菜单路径 - 支持新的路由结构
 * @param {string} currentPath - 当前路由路径
 * @param {Array} menuItems - 菜单配置项
 * @param {Array} breadcrumbs - 当前面包屑路径
 * @returns {Array|null} - 找到的面包屑路径或null
 */
function findMenuPath(currentPath, menuItems = menuConfig, breadcrumbs = []) {
  for (const item of menuItems) {
    const newBreadcrumbs = [...breadcrumbs, {
      title: item.title,
      path: item.path || null
    }]

    // 直接匹配当前路径
    if (item.path === currentPath) {
      return newBreadcrumbs
    }

    // 如果有子菜单，递归查找
    if (item.children) {
      for (const child of item.children) {
        // 检查子路由路径匹配（包括动态路由）
        if (child.path === currentPath || currentPath.startsWith(child.path.split('/:')[0])) {
          return [...newBreadcrumbs, {
            title: child.title,
            path: child.showInMenu !== false ? child.path : null
          }]
        }
      }

      // 继续递归查找
      const childPath = findMenuPath(currentPath, item.children, newBreadcrumbs)
      if (childPath) {
        return childPath
      }
    }
  }

  return null
}

/**
 * 根据路由参数和查询参数生成动态标题
 * @param {Object} route - 当前路由对象
 * @returns {string|null} - 动态生成的标题
 */
function getDynamicTitle(route) {
  const { path, params, query, meta } = route
  
  // 处理用户编辑页面的特殊逻辑
  if (path.includes('/user-edit')) {
    if (params.id) {
      // 根据查询参数判断是查看还是编辑模式
      if (query.mode === 'view') {
        return '查看用户'
      } else {
        return '编辑用户'
      }
    } else {
      return '新增用户'
    }
  }
  
  // 处理用户创建页面
  if (path.includes('/user-create-v2')) {
    return '新增用户'
  }
  
  // 其他动态路由可以在这里添加处理逻辑
  
  return meta?.title || null
}

/**
 * 获取面包屑的父级路径 - 使用新路由结构自动查找
 * @param {string} currentPath - 当前路径
 * @param {Array} menuItems - 菜单配置项
 * @returns {string|null} - 父级路径
 */
function getParentPath(currentPath, menuItems = menuConfig) {
  for (const item of menuItems) {
    if (item.children) {
      for (const child of item.children) {
        // 检查是否匹配子路由（包括动态路由）
        if (child.path === currentPath || currentPath.startsWith(child.path.split('/:')[0])) {
          return item.path
        }
      }
    }
  }

  return null
}

/**
 * 生成面包屑导航
 * @param {Object} route - 当前路由对象
 * @returns {Array} - 面包屑数组
 */
export function generateBreadcrumbs(route) {
  const breadcrumbs = []
  
  // 总是添加首页作为根节点
  breadcrumbs.push({
    title: '首页',
    path: '/admin'
  })
  
  // 如果当前就是首页，直接返回
  if (route.path === '/admin' || route.path === '/admin/dashboard') {
    return breadcrumbs
  }
  
  // 首先尝试通过菜单配置查找完整路径
  const menuPath = findMenuPath(route.path)
  if (menuPath && menuPath.length > 1) {
    // 跳过首页，添加菜单路径（从第二个开始，因为第一个是首页）
    breadcrumbs.push(...menuPath.slice(1))
    
    // 检查是否需要添加动态标题
    const dynamicTitle = getDynamicTitle(route)
    if (dynamicTitle && dynamicTitle !== breadcrumbs[breadcrumbs.length - 1].title) {
      breadcrumbs.push({
        title: dynamicTitle,
        path: null
      })
    }
    
    return breadcrumbs
  }
  
  // 如果菜单中找不到，尝试通过父级路径构建
  const parentPath = getParentPath(route.path)
  if (parentPath) {
    const parentMenuPath = findMenuPath(parentPath)
    if (parentMenuPath && parentMenuPath.length > 1) {
      // 添加父级路径
      breadcrumbs.push(...parentMenuPath.slice(1))

      // 添加当前页面
      const dynamicTitle = getDynamicTitle(route)
      breadcrumbs.push({
        title: dynamicTitle || route.meta?.title || '未知页面',
        path: null
      })

      return breadcrumbs
    }
  }
  
  // 最后回退方案：使用路由的 matched 信息
  if (route.matched && route.matched.length > 0) {
    // 跳过 Layout 路由，只处理子路由
    const childRoutes = route.matched.filter(match => 
      match.path !== '/admin' && match.meta?.title
    )
    
    for (const match of childRoutes) {
      const dynamicTitle = getDynamicTitle(route)
      breadcrumbs.push({
        title: dynamicTitle || match.meta.title,
        path: match.path === route.path ? null : match.path
      })
    }
  }
  
  // 如果还是没有找到合适的面包屑，使用当前路由的标题
  if (breadcrumbs.length === 1) {
    const dynamicTitle = getDynamicTitle(route)
    breadcrumbs.push({
      title: dynamicTitle || route.meta?.title || '当前页面',
      path: null
    })
  }
  
  return breadcrumbs
}

/**
 * 面包屑项目数据结构
 * @typedef {Object} BreadcrumbItem
 * @property {string} title - 显示标题
 * @property {string|null} path - 路由路径，null表示不可点击
 */
