/* 后台管理页面通用样式 */

/* 页面容器 */
.admin-page {
  padding: 0;
  background-color: transparent;
  min-height: 100%;
}

/* 页面标题区域 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.back-button {
  color: #606266;
  border-radius: 6px;
  font-weight: 500;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 20px;
  background: white;
}

.search-card :deep(.el-card__body) {
  padding: 20px;
}

.search-card .el-form {
  margin-bottom: 0;
}

.search-card .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-card .el-form-item:last-child {
  margin-right: 0;
}

/* 表格卡片 */
.table-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.table-card :deep(.el-card__body) {
  padding: 0;
}

/* 表单卡片 */
.form-card {
  margin-bottom: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-card :deep(.el-card__body) {
  padding: 30px;
}

/* 表单操作区域 */
.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-actions .el-button {
  min-width: 100px;
  border-radius: 6px;
  font-weight: 500;
}

/* 表单样式优化 */
.form-card :deep(.el-form) {
  max-width: 800px;
}

.form-card :deep(.el-form-item) {
  margin-bottom: 24px;
}

.form-card :deep(.el-form-item__label) {
  font-weight: 600;
  color: #606266;
}

.form-card :deep(.el-input),
.form-card :deep(.el-select),
.form-card :deep(.el-textarea),
.form-card :deep(.el-date-editor) {
  width: 100%;
}

.form-card :deep(.el-input__wrapper),
.form-card :deep(.el-textarea__inner) {
  border-radius: 6px;
}

.form-card :deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

.form-card :deep(.el-date-editor .el-input__wrapper) {
  border-radius: 6px;
}

/* 单选框样式 */
.form-card :deep(.el-radio-group) {
  display: flex;
  gap: 20px;
}

.form-card :deep(.el-radio) {
  margin-right: 0;
}

.form-card :deep(.el-radio__label) {
  font-weight: 500;
}

/* 文本域样式 */
.form-card :deep(.el-textarea) {
  width: 100%;
}

.form-card :deep(.el-textarea__inner) {
  resize: vertical;
  min-height: 80px;
}

/* 禁用状态样式 */
.form-card :deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}

/* 表格样式优化 */
.table-card :deep(.el-table) {
  border-radius: 0 0 8px 8px;
}

.table-card :deep(.el-table__header) {
  background-color: #fafafa;
}

.table-card :deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

.table-card :deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

.table-card :deep(.el-table__row:hover) {
  background-color: #f8f9fa;
}

/* 分页样式 */
.table-card :deep(.el-pagination) {
  padding: 20px;
  justify-content: flex-end;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
}

/* 按钮样式优化 */
.header-actions .el-button {
  border-radius: 6px;
  font-weight: 500;
}

.search-card .el-button {
  border-radius: 6px;
  font-weight: 500;
}

/* 输入框样式优化 */
.search-card .el-input,
.search-card .el-select {
  border-radius: 6px;
}

.search-card .el-input :deep(.el-input__wrapper) {
  border-radius: 6px;
}

.search-card .el-select :deep(.el-input__wrapper) {
  border-radius: 6px;
}

/* 卡片悬停效果 */
.search-card,
.table-card,
.form-card,
.form-actions {
  transition: box-shadow 0.3s ease;
}

.search-card:hover,
.table-card:hover,
.form-card:hover,
.form-actions:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 加载状态样式 */
.table-card :deep(.el-loading-mask) {
  border-radius: 8px;
}

/* 空数据样式 */
.table-card :deep(.el-table__empty-block) {
  padding: 60px 0;
}

.table-card :deep(.el-table__empty-text) {
  color: #909399;
  font-size: 14px;
}

/* 表单验证错误样式 */
.form-card :deep(.el-form-item.is-error .el-input__wrapper) {
  border-color: #f56c6c;
  box-shadow: 0 0 0 1px #f56c6c inset;
}

.form-card :deep(.el-form-item.is-error .el-textarea__inner) {
  border-color: #f56c6c;
  box-shadow: 0 0 0 1px #f56c6c inset;
}

.form-card :deep(.el-form-item__error) {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
}

/* 加载状态 */
.form-actions .el-button.is-loading {
  pointer-events: none;
}

/* 成功状态样式 */
.form-card :deep(.el-form-item.is-success .el-input__wrapper) {
  border-color: #67c23a;
}

.form-card :deep(.el-form-item.is-success .el-textarea__inner) {
  border-color: #67c23a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-page {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .search-card .el-form {
    display: flex;
    flex-direction: column;
  }
  
  .search-card .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .search-card .el-form-item:last-child {
    margin-bottom: 0;
  }
  
  .form-card :deep(.el-card__body) {
    padding: 20px;
  }
  
  .form-card :deep(.el-form) {
    max-width: 100%;
  }
  
  .form-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .form-actions .el-button {
    width: 200px;
  }
}

/* 内联表单样式 */
:deep(.el-form--inline .el-form-item) {
  margin-right: 20px;
}

/* 富文本编辑器样式调整 */
:deep(.rich-editor) {
  max-width: 100%;
}

/* 上传组件样式调整 */
:deep(.el-upload--picture-card) {
  width: 120px;
  height: 120px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 120px;
  height: 120px;
}

/* 商品图片样式 */
.table-card :deep(.el-table .product-image) {
  border-radius: 8px;
  object-fit: cover;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 特殊状态样式 */
.table-card :deep(.el-table .admin-username) {
  font-weight: 600;
  color: #409eff;
}

.table-card :deep(.el-table .super-admin) {
  color: #f56c6c;
  font-weight: 600;
}
