# SimpleTable 自定义插槽使用指南

## 🚧 SimpleTable.vue 自定义插槽功能增强 🚧

**功能等级**: 🟡 MODERATE - 组件功能扩展
**实现状态**: ✅ 已完成 - 支持自定义插槽
**工程量**: 2个文件，已完成实现

## 核心功能概览

### :bullseye: 新增功能
1. **自定义插槽支持** - 支持完全自定义列内容
2. **灵活事件绑定** - 在插槽中自由绑定事件
3. **丰富插槽参数** - 提供 row, index, value, column 参数
4. **保持兼容性** - 与现有功能完全兼容

### :world_map: 功能架构
```mermaid
graph TD
    A[SimpleTable] --> B[列类型系统]
    B --> C[内置类型]
    B --> D[自定义插槽]

    C --> E[text 文本]
    C --> F[tag 标签]
    C --> G[image 图片]
    C --> H[datetime 日期]
    C --> I[currency 货币]

    D --> J[slot 插槽]
    D --> K[自定义组件]
    D --> L[自定义事件]
```

## 使用方法

### 1. 自定义插槽 (Slot)

#### 基础插槽用法 - 开关组件示例
```vue
<template>
  <SimpleTable :data="tableData" :config="tableConfig">
    <!-- 登录状态插槽 - 使用开关组件 -->
    <template #login_status="{ row, value }">
      <el-switch
        :model-value="value === '1'"
        :active-value="'1'"
        :inactive-value="'0'"
        active-text="在线"
        inactive-text="离线"
        @change="(newValue) => handleLoginStatusChange(newValue, row)"
      />
    </template>

    <!-- 自定义操作列 -->
    <template #operation="{ row, index }">
      <el-button
        type="primary"
        size="small"
        @click="handleEdit(row)"
      >
        编辑
      </el-button>
      <el-button
        type="danger"
        size="small"
        @click="handleDelete(row, index)"
      >
        删除
      </el-button>
    </template>
  </SimpleTable>
</template>

<script setup>
const tableConfig = {
  columns: [
    { prop: 'username', label: '用户名' },
    {
      prop: 'login_status',
      label: '登录状态',
      type: 'slot',
      width: 120
    },
    {
      prop: 'operation',
      label: '操作',
      type: 'slot',
      width: 160
    }
  ]
}

// 处理登录状态变化
const handleLoginStatusChange = async (value, row) => {
  try {
    // 显示确认对话框
    await ElMessageBox.confirm(`确定要修改用户状态吗？`)

    // 调用API更新状态
    await updateUserStatus(row.id, value)

    // 更新本地数据
    row.login_status = value

    ElMessage.success('状态更新成功')
  } catch (error) {
    // 处理错误或取消操作
    console.log('操作取消或失败')
  }
}
</script>
```

#### 插槽参数说明
```typescript
interface SlotProps {
  row: any        // 当前行的完整数据
  index: number   // 当前行的索引
  value: any      // 当前单元格的值 (row[column.prop])
  column: object  // 当前列的配置对象
}
```

### 3. 智能类型推断

系统会根据属性名自动推断列类型：

```javascript
// 自动推断为 switch 类型
const columns = [
  'enable',      // 启用状态
  'enabled',     // 启用状态
  'active',      // 活跃状态
  'status',      // 状态
  'online',      // 在线状态
  'visible'      // 可见状态
]

// 自动推断为其他类型
const columns = [
  'avatar',      // image 类型
  'createTime',  // datetime 类型
  'price',       // currency 类型
  'role'         // tag 类型
]
```

## 实际应用示例

### 后台用户管理页面配置

<augment_code_snippet path="src/views/admin-user/admin_user_list.js" mode="EXCERPT">
```javascript
const tableConfig = {
  columns: [
    { prop: 'id', label: 'ID', width: 80 },
    { prop: 'username', label: '用户名', width: 150 },
    {
      prop: 'login_status',
      label: '登录状态',
      type: 'slot',  // 使用插槽类型
      width: 120
    }
  ]
}
```
</augment_code_snippet>

### 模板中的插槽使用

<augment_code_snippet path="src/views/admin-user/admin_user_list.vue" mode="EXCERPT">
```vue
<SimpleTable
  :data="tableData"
  :config="tableConfig"
  @action="handleTableAction"
  @selection-change="handleSelectionChange"
  @page-change="handlePageChange"
  @size-change="handleSizeChange"
>
  <!-- 登录状态插槽 -->
  <template #login_status="{ row, value }">
    <el-switch
      :model-value="value === '1'"
      :active-value="'1'"
      :inactive-value="'0'"
      active-text="在线"
      inactive-text="离线"
      @change="(newValue) => handleLoginStatusChange(newValue, row)"
    />
  </template>
</SimpleTable>
```
</augment_code_snippet>

### 事件处理逻辑

<augment_code_snippet path="src/views/admin-user/admin_user_list.js" mode="EXCERPT">
```javascript
const handleLoginStatusChange = async (value, row) => {
  try {
    // 显示确认对话框
    const action = value === '1' ? '启用' : '禁用'
    await ElMessageBox.confirm(
      `确定要${action}管理员 "${row.username}" 的登录状态吗？`,
      '状态变更确认'
    )

    // 模拟API调用
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 500))

    // 更新本地数据
    const targetRow = tableData.value.find(item => item.id === row.id)
    if (targetRow) {
      targetRow.login_status = value
    }

    ElMessage.success(`管理员 "${row.username}" 登录状态已${action}`)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('状态变更失败')
    }
    // 如果操作失败或取消，刷新数据恢复原状态
    fetchAdminUserList()
  } finally {
    loading.value = false
  }
}
```
</augment_code_snippet>

## 配置参考

### 插槽类型配置
```typescript
interface SlotColumn {
  prop: string
  label: string
  type: 'slot'
  width?: number
  minWidth?: number
  sortable?: boolean
}
```

### 插槽参数
```typescript
interface SlotProps {
  row: any        // 当前行的完整数据
  index: number   // 当前行的索引
  value: any      // 当前单元格的值 (row[column.prop])
  column: object  // 当前列的配置对象
}
```

### 使用示例
```vue
<SimpleTable :data="tableData" :config="tableConfig">
  <template #column_name="{ row, index, value, column }">
    <!-- 自定义内容 -->
  </template>
</SimpleTable>
```

## 最佳实践

### 1. 插槽使用最佳实践
- **保持简洁**: 插槽内容应该简洁明了，避免复杂的嵌套结构
- **事件处理**: 在插槽中绑定的事件应该有适当的错误处理
- **确认操作**: 对于重要操作（如状态变更），提供确认对话框
- **用户反馈**: 提供清晰的成功/失败反馈信息

### 2. 性能优化建议
- **避免复杂计算**: 不要在插槽中进行复杂的计算或数据处理
- **合理设置宽度**: 根据插槽内容合理设置列宽度
- **控制组件数量**: 避免在插槽中使用过于复杂的组件
- **考虑响应式**: 确保插槽内容在不同屏幕尺寸下正常显示

### 3. 开发建议
- **权限控制**: 在插槽中考虑用户权限，禁用无权限的操作
- **状态管理**: 合理管理插槽中组件的状态
- **错误恢复**: 提供操作失败时的数据恢复机制
- **测试覆盖**: 确保插槽功能有充分的测试覆盖

---

**文档版本**: v1.0.0
**更新时间**: 2025-07-17
**维护者**: AI Assistant
